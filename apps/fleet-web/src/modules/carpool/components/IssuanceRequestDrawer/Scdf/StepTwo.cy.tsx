/// <reference types="@testing-library/cypress" />
import { zodResolver } from '@hookform/resolvers/zod'
import { But<PERSON>, Stack } from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { useForm } from 'react-hook-form'
import { match } from 'ts-pattern'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { carpoolEndpointMocks } from 'src/cypress-ct/mocks/endpoints/carpool'
import { getInputInsideByTestId, mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import { generateBookingSchema, type BookingFormSchema } from './schema'
import StepTwo from './StepTwo'
import type { LocationAutocompleteOption } from './types'

// Test selectors using data-testid
const pickupTimeDataTestId = 'StepTwo-PickupTime'
const dropoffTimeDataTestId = 'StepTwo-DropoffTime'
const journeyTypeDataTestId = 'StepTwo-JourneyType'
const pickupLocationDataTestId = 'StepTwo-PickupLocation'

// Mock data for testing
const mockLocationOptions = {
  array: [
    { id: 1, label: 'Main Office' },
    { id: 2, label: 'Branch Office' },
    { id: 3, label: 'Warehouse' },
  ] as Array<LocationAutocompleteOption>,
  byId: new Map([
    [1, { id: 1, label: 'Main Office' }],
    [2, { id: 2, label: 'Branch Office' }],
    [3, { id: 3, label: 'Warehouse' }],
  ]),
}

const defaultFormValues: BookingFormSchema = {
  purposeOfRequest: 1,
  requestDescription: '',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: null,
  vehicleId: null,
  numberOfPassengers: null,
  driverSelection: 'any' as const,
  driverId: null,
  vehicleCommanderSelection: 'any' as const,
  vehicleCommanderId: null,
  pickupTime: null,
  pickupLocation: null,
  dropoffTime: null,
  journeyType: 'return' as const,
  journeys: [],
  equipmentType: 'no-equipment' as const,
  equipmentIds: [],
  uploadedImages: [],
  equipmentAttachmentIds: [],
  remarks: '',
}

// Test wrapper component
function TestStepTwo({
  disabled = false,
  initialValues = defaultFormValues,
}: {
  disabled?: boolean
  initialValues?: Partial<BookingFormSchema>
}) {
  const schema = generateBookingSchema({
    carpoolBookingInAdvance: 24,
    carpoolBookingInAdvanceUnit: 'hours',
    carpoolMaximumBookingTime: 8,
    carpoolMaximumBookingTimeUnit: 'hours',
    carpoolAllowBackDateBooking: false,
    bookingPurposeByIdMap: new Map([[1, { id: 1, label: 'Official Business' }]]),
    mode: 'create',
  })

  const {
    control,
    setValue: setFormValue,
    trigger,
  } = useForm<BookingFormSchema>({
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: { ...defaultFormValues, ...initialValues },
  })

  const maxDateTimeInAdvance = DateTime.now().plus({ hours: 24 })

  const handleNext = async () =>
    await trigger([
      'pickupTime',
      'dropoffTime',
      'pickupLocation',
      'journeyType',
      'journeys',
    ])

  return (
    <Stack>
      <StepTwo
        control={control}
        locationOptions={mockLocationOptions}
        maxDateTimeInAdvance={maxDateTimeInAdvance}
        carpoolMaximumBookingTime={8}
        carpoolMaximumBookingTimeUnit="hours"
        disabled={disabled}
        trigger={trigger}
        setFormValue={setFormValue}
      />
      <Button
        onClick={handleNext}
        data-testid="StepTwo-NextButton"
      >
        Next
      </Button>
    </Stack>
  )
}

const mountStepTwo = (props?: Parameters<typeof TestStepTwo>[0]) => {
  // Mock carpool API endpoints
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    match(req.body.method)
      .with('ct_fleet_get_additional_locations', () => {
        req.reply(carpoolEndpointMocks.ct_fleet_get_additional_locations())
      })
      .otherwise(() => {
        // No-op for other methods
      })
  })

  mountWithProviders(<TestStepTwo {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user().mockState,
      },
    },
  })
}

const type5Left = '{leftArrow}{leftArrow}{leftArrow}{leftArrow}{leftArrow}'

describe('StepTwo Component', () => {
  it('should fill and validate form', () => {
    mountStepTwo()

    // trigger the form
    cy.findByTestId('StepTwo-NextButton').click()

    // Check for validation error for pickup time
    cy.findByTestId(pickupTimeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('contain.text', messages.required)

    // Check for validation error for dropoff time
    cy.findByTestId(dropoffTimeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('contain.text', messages.required)

    // Check for validation error for pickup location
    cy.findByTestId(pickupLocationDataTestId)
      .find('.MuiFormHelperText-root')
      .should('contain.text', messages.required)

    // Click on pickup time field
    cy.findByTestId(pickupTimeDataTestId).find('.MuiInputAdornment-root').click()

    // Should open date/time picker
    cy.get('.MuiPickersPopper-root').should('be.visible')

    // The field should have a value set (current hour)
    getInputInsideByTestId(pickupTimeDataTestId).should('not.have.value', '')

    // close pickup time field
    cy.findByTestId(pickupTimeDataTestId).find('.MuiInputAdornment-root').click()

    // Set the pickup time to 2 hours from now by typing
    const pickupTime = DateTime.now().plus({ hours: 2 })
    const pickupTimeString = pickupTime.toFormat('yyyyLLddHHmm')
    getInputInsideByTestId(pickupTimeDataTestId).type(
      `{selectall}{backspace}${pickupTimeString}`,
    )

    // Set the dropoff time to 3 hours from now by typing
    const dropoffTime = DateTime.now().plus({ hours: 3 })
    const dropoffTimeString = dropoffTime.toFormat('yyyyLLddHHmm')
    getInputInsideByTestId(dropoffTimeDataTestId).type(
      `${type5Left}${dropoffTimeString}`,
    )

    /**************************************** Journey Type ****************************************/

    // Default (return journey) should show "Start Location & Destination"
    cy.findByTestId(pickupLocationDataTestId + '-TextField').should(
      'contain.text',
      'Start Location & Destination',
    )
    // Return journey should not show journeys list
    cy.get('[data-testid*="DraggableJourney"]').should('not.exist')

    // Test single journey selection
    getInputInsideByTestId(journeyTypeDataTestId + '-single').click()
    getInputInsideByTestId(journeyTypeDataTestId + '-single').should('be.checked')
    getInputInsideByTestId(journeyTypeDataTestId + '-return').should('not.be.checked')
    cy.findByTestId(pickupLocationDataTestId + '-TextField').should(
      'contain.text',
      'Start Location',
    )

    // set start location
    cy.findByTestId(pickupLocationDataTestId).click()

    // Should show location options
    cy.contains(mockLocationOptions.array[1].label).should('be.visible')

    // Select the location
    cy.contains(mockLocationOptions.array[1].label).click()

    // The autocomplete should show the selected value
    getInputInsideByTestId(pickupLocationDataTestId).should(
      'contain.value',
      mockLocationOptions.array[1].label,
    )

    // set dropoff location
    cy.findByTestId('DraggableJourney-0').should('exist')

    // Click on the location autocomplete
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').click()

    // Type to search for a location
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').type(
      mockLocationOptions.array[0].label.substring(0, 4),
    )

    // Should show location options
    cy.contains(mockLocationOptions.array[0].label).should('be.visible')

    // Select the location
    cy.contains(mockLocationOptions.array[0].label).click()

    // The autocomplete should show the selected value
    getInputInsideByTestId('DraggableJourney-LocationAutocomplete-0').should(
      'contain.value',
      mockLocationOptions.array[0].label,
    )

    // should no error
    cy.findByTestId(pickupLocationDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    cy.findByTestId('JourneysList-Error').should('not.exist')

    // Test multiple stops journey selection
    cy.findByTestId(journeyTypeDataTestId + '-multiple').click()
    getInputInsideByTestId(journeyTypeDataTestId + '-multiple').should('be.checked')
    getInputInsideByTestId(journeyTypeDataTestId + '-single').should('not.be.checked')
    cy.findByTestId(pickupLocationDataTestId + '-TextField').should(
      'contain.text',
      'Start Location',
    )
    cy.findByTestId('DraggableJourney-0').should('exist')

    // Add location button should be visible
    cy.findByTestId('JourneysList-AddLocation-Button').should('be.visible')

    // Click add location
    cy.findByTestId('JourneysList-AddLocation-Button').click()

    // Should now have two journeys
    cy.findByTestId('DraggableJourney-0').should('exist')
    cy.findByTestId('DraggableJourney-1').should('exist')

    // Remove buttons should be visible when there are multiple journeys
    cy.findByTestId('DraggableJourney-Remove-0').should('be.visible')
    cy.findByTestId('DraggableJourney-Remove-1').should('be.visible')

    // Remove the second journey
    cy.findByTestId('DraggableJourney-Remove-1').click()

    // Should be back to one journey
    cy.findByTestId('DraggableJourney-0').should('exist')
    cy.findByTestId('DraggableJourney-1').should('not.exist')

    // Click add location
    cy.findByTestId('JourneysList-AddLocation-Button').click()

    // set the locations value

    // Click on the location autocomplete
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').click()

    // Type to search for a location
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').type(
      mockLocationOptions.array[0].label.substring(0, 4),
    )

    // Should show location options
    cy.contains(mockLocationOptions.array[0].label).should('be.visible')

    // Select the location
    cy.contains(mockLocationOptions.array[0].label).click()

    // Click on the location autocomplete 1
    cy.findByTestId('DraggableJourney-LocationAutocomplete-1').click()

    // Type to search for a location
    cy.findByTestId('DraggableJourney-LocationAutocomplete-1').type(
      mockLocationOptions.array[1].label.substring(0, 4),
    )

    // Should show location options
    cy.contains(mockLocationOptions.array[1].label).should('be.visible')

    // Select the location
    cy.contains(mockLocationOptions.array[1].label).click()

    // The autocomplete should show the selected value
    getInputInsideByTestId('DraggableJourney-LocationAutocomplete-1').should(
      'contain.value',
      mockLocationOptions.array[1].label,
    )

    // should no error
    cy.findByTestId(pickupLocationDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    // Test return journey selection
    cy.findByTestId(journeyTypeDataTestId + '-return').click()
    getInputInsideByTestId(journeyTypeDataTestId + '-return').should('be.checked')
    getInputInsideByTestId(journeyTypeDataTestId + '-multiple').should('not.be.checked')
    cy.findByTestId(pickupLocationDataTestId + '-TextField').should(
      'contain.text',
      'Start Location & Destination',
    )

    // trigger the form, should not error
    cy.findByTestId('StepTwo-NextButton').click()

    cy.findByTestId(pickupTimeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    cy.findByTestId(dropoffTimeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    cy.findByTestId(pickupLocationDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')
  })

  it('should check the pickup time cannot be in the past', () => {
    mountStepTwo()

    // Set pickup time to 1 hour in the past
    const pastTime = DateTime.now().minus({ hours: 1 })
    const pastTimeString = pastTime.toFormat('yyyyLLddHHmm')

    // Type the past time
    getInputInsideByTestId(pickupTimeDataTestId).type(`${type5Left}${pastTimeString}`)

    // Click outside to close picker and trigger validation
    cy.findByTestId('StepTwo-NextButton').click()

    // Check for validation error
    cy.findByTestId(pickupTimeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('be.visible')
      .should('contain.text', 'Pick-up time must be after current time')
  })

  it('should check the pickup time cannot be after dropoff time', () => {
    mountStepTwo()

    // Set pickup time to 3 hours from now
    const pickupTime = DateTime.now().plus({ hours: 3 })
    const pickupTimeString = pickupTime.toFormat('yyyyLLddHHmm')

    // Set dropoff time to 2 hours from now (before pickup time)
    const dropoffTime = DateTime.now().plus({ hours: 2 })
    const dropoffTimeString = dropoffTime.toFormat('yyyyLLddHHmm')

    // Click on pickup time field and set it
    getInputInsideByTestId(pickupTimeDataTestId).type(`${type5Left}${pickupTimeString}`)

    // Click on dropoff time field and set it
    getInputInsideByTestId(dropoffTimeDataTestId).type(
      `${type5Left}${dropoffTimeString}`,
    )

    // Click outside to trigger validation
    cy.findByTestId('StepTwo-NextButton').click()

    // Check for validation error on dropoff time
    cy.findByTestId(dropoffTimeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('be.visible')
      .should('contain.text', 'Drop-off time must be after pick-up time')
  })

  it('should allow toggling between POI and free text mode', () => {
    mountStepTwo()

    // Switch to single journey mode
    cy.findByTestId(journeyTypeDataTestId + '-single').click()

    // Toggle mode button should be visible
    cy.findByTestId('DraggableJourney-ToggleMode-0').should('be.visible')

    // Click toggle mode button
    cy.findByTestId('DraggableJourney-ToggleMode-0').click()

    // Should be able to type free text
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').type(
      'Custom Location Name',
    )

    // Toggle back to POI mode
    cy.findByTestId('DraggableJourney-ToggleMode-0').click()

    // Should show POI options again
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').clear()
    cy.findByTestId('DraggableJourney-LocationAutocomplete-0').type('Main')
    cy.contains('Main Office').should('be.visible')
  })

  it('should disable all form fields when disabled prop is true', () => {
    mountStepTwo({ disabled: true })

    // All interactive fields should be disabled
    getInputInsideByTestId(pickupTimeDataTestId).should('be.disabled')
    getInputInsideByTestId(dropoffTimeDataTestId).should('be.disabled')

    getInputInsideByTestId(journeyTypeDataTestId + '-single').should('be.disabled')
    getInputInsideByTestId(journeyTypeDataTestId + '-return').should('be.disabled')
    getInputInsideByTestId(journeyTypeDataTestId + '-multiple').should('be.disabled')
    getInputInsideByTestId(pickupLocationDataTestId).should('be.disabled')
  })
})
