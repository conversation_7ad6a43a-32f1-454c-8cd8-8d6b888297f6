/// <reference types="@testing-library/cypress" />
import { zodResolver } from '@hookform/resolvers/zod'
import { Button, Stack } from '@karoo-ui/core'
import { createMemoryHistory } from 'history'
import { useForm } from 'react-hook-form'
import { match } from 'ts-pattern'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import {
  carpoolEndpointMocks,
  mockEquipmentData,
  mockParsedDriverData,
  restApiMocks,
} from 'src/cypress-ct/mocks/endpoints/carpool'
import {
  cyExpect,
  getInputInsideByTestId,
  mountWithProviders,
} from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import { EQUIPMENT_TYPE, JOURNEY_TYPE } from './constant'
import { generateBookingSchema, type BookingFormSchema } from './schema'
import StepThree from './StepThree'
import type { VehicleTypeAutocompleteOption } from './types'

// Test selectors using data-testid
const vehicleIdDataTestId = 'StepThree-VehicleId'
const vehicleTypeDataTestId = 'StepThree-VehicleType'
const numberOfPassengersDataTestId = 'StepThree-NumberOfPassengers'
const driverSelectionDataTestId = 'StepThree-DriverSelection'
const driverSelectDataTestId = 'StepThree-DriverSelect'
const vehicleCommanderTitleDataTestId = 'StepThree-VehicleCommanderTitle'
const vehicleCommanderSelectionDataTestId = 'StepThree-VehicleCommanderSelection'
const vehicleCommanderSelectDataTestId = 'StepThree-VehicleCommanderSelect'
const equipmentTypeDataTestId = 'StepThree-EquipmentType'
const equipmentSelectDataTestId = 'StepThree-EquipmentSelect'
const equipmentOptionDataTestIdPrefix = 'StepThree-EquipmentOption-'
const uploadedImageDataTestIdPrefix = 'AttachmentItem-'
const uploadedImageFileNameSuffix = '-FileName'
const previewImageModalTestId = 'ImagePreviewModal'

const remarksInputDataTestId = 'RemarksInput'

// Mock data for testing
const mockVehicleTypeOptions = {
  array: [
    { id: 1, label: 'Sedan' },
    { id: 2, label: 'SUV' },
    { id: 3, label: 'Van' },
  ] as Array<VehicleTypeAutocompleteOption>,
  byId: new Map([
    [1, { id: 1, label: 'Sedan' }],
    [2, { id: 2, label: 'SUV' }],
    [3, { id: 3, label: 'Van' }],
  ]),
}

const defaultFormValues: BookingFormSchema = {
  purposeOfRequest: 1,
  requestDescription: '',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: null,
  vehicleId: null,
  numberOfPassengers: null,
  driverSelection: 'any' as const,
  driverId: null,
  vehicleCommanderSelection: 'any' as const,
  vehicleCommanderId: null,
  pickupTime: new Date(),
  pickupLocation: 1,
  dropoffTime: new Date(),
  journeyType: 'return' as const,
  journeys: [],
  equipmentType: 'no-equipment' as const,
  equipmentIds: [],
  uploadedImages: [],
  equipmentAttachmentIds: [],
  remarks: '',
}

// Test wrapper component
function TestStepThree({
  disabled = false,
  isKeyCollected = false,
  mode = 'create',
  vehicleIdExists = false,
  initialValues = defaultFormValues,
  onFormSubmit,
}: {
  disabled?: boolean
  isKeyCollected?: boolean
  mode?: 'create' | 'edit' | 'approve' | 'view'
  vehicleIdExists?: boolean
  initialValues?: Partial<BookingFormSchema>
  onFormSubmit?: (data: BookingFormSchema, isValid: boolean) => void
}) {
  const schema = generateBookingSchema({
    carpoolBookingInAdvance: 24,
    carpoolBookingInAdvanceUnit: 'hours',
    carpoolMaximumBookingTime: 8,
    carpoolMaximumBookingTimeUnit: 'hours',
    carpoolAllowBackDateBooking: false,
    bookingPurposeByIdMap: new Map([[1, { id: 1, label: 'Official Business' }]]),
    mode,
  })

  const {
    control,
    setValue: setFormValue,
    trigger,
    getValues,
  } = useForm<BookingFormSchema>({
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: { ...defaultFormValues, ...initialValues },
  })

  const handleNext = async () => {
    const isValid = await trigger([
      'driverSelection',
      'driverId',
      'vehicleTypeId',
      'vehicleId',
      'numberOfPassengers',
      'vehicleCommanderSelection',
      'vehicleCommanderId',
      'equipmentIds',
    ])

    const formData = getValues()
    onFormSubmit?.(formData, isValid)

    return isValid
  }

  return (
    <Stack
      sx={{ height: '100%', overflowY: 'auto' }}
      spacing={2}
    >
      <StepThree
        control={control}
        setFormValue={setFormValue}
        vehicleTypeOptions={mockVehicleTypeOptions}
        disabled={disabled}
        isKeyCollected={isKeyCollected}
        mode={mode}
        vehicleIdExists={vehicleIdExists}
      />
      <Button
        onClick={handleNext}
        data-testid="StepThree-NextButton"
      >
        Next
      </Button>
    </Stack>
  )
}

const mountStepThree = (props?: Parameters<typeof TestStepThree>[0]) => {
  // Mock JSON-RPC API calls
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    match(req.body.method)
      .with('ct_fleet_get_booking_options', () =>
        req.reply(carpoolEndpointMocks.ct_fleet_get_booking_options()),
      )
      .with('ct_fleet_carpool_get_booking_options', () =>
        req.reply(carpoolEndpointMocks.ct_fleet_carpool_get_booking_options()),
      )
      .with('ct_fleet_get_available_vehicles', () =>
        req.reply(carpoolEndpointMocks.ct_fleet_get_available_vehicles()),
      )
      .otherwise(() => {})
  })

  // Mock REST API calls
  cy.intercept('GET', '/scdf/accessory', restApiMocks['GET /scdf/accessory']())

  cy.intercept(
    {
      method: 'GET',
      pathname: '/scdf/vehicle/vehicle-for-driver',
      query: { clientDriverId: '*' },
    },
    (req) => {
      const url = new URL(req.url)
      const clientDriverId = url.searchParams.get('clientDriverId')
      return restApiMocks['GET /scdf/vehicle/vehicle-for-driver']({
        clientDriverId: clientDriverId || undefined,
      })
    },
  )

  // Mock image upload and delete APIs
  cy.intercept(
    'POST',
    '/scdf/booking/uploadSingleImageTemp',
    restApiMocks['POST /scdf/booking/uploadSingleImageTemp'](),
  )

  cy.intercept(
    'POST',
    '/scdf/booking/deleteTempSingleImage',
    restApiMocks['POST /scdf/booking/deleteTempSingleImage'](),
  )

  const history = createMemoryHistory()

  mountWithProviders(<TestStepThree {...props} />, {
    history,
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user().mockState,
        drivers: duxsMocks.drivers({ drivers: mockParsedDriverData }),
        admin: duxsMocks.admin,
      },
    },
  })
}

describe('StepThree Component', () => {
  it('should validate form and capture submitted values', () => {
    let submittedData: BookingFormSchema | null = null
    let isFormValid = false

    mountStepThree({
      onFormSubmit: (data, valid) => {
        submittedData = data
        isFormValid = valid
      },
    })

    // trigger the form to check initial validation errors
    cy.findByTestId('StepThree-NextButton').click()

    // Check for validation errors for required fields
    cy.findByTestId(vehicleTypeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('contain.text', messages.required)

    cy.findByTestId(numberOfPassengersDataTestId)
      .find('.MuiFormHelperText-root')
      .should(
        'contain.text',
        'Number of passengers is required and must be greater than 0',
      )

    // Verify form is invalid initially
    cyExpect(isFormValid).toEq(false)

    cy.log(
      '/**************************************** Vehicle Section ****************************************/',
    )

    // Check vehicle section is visible
    cy.findByTestId(vehicleTypeDataTestId).should('be.visible')
    cy.findByTestId(vehicleIdDataTestId).should('not.exist')
    cy.findByTestId(numberOfPassengersDataTestId).should('be.visible')

    // Select vehicle type
    cy.findByTestId(vehicleTypeDataTestId).click()

    // Check all options are visible
    cy.contains(mockVehicleTypeOptions.array[0].label).should('be.visible')
    cy.contains(mockVehicleTypeOptions.array[1].label).should('be.visible')
    cy.contains(mockVehicleTypeOptions.array[2].label).should('be.visible')

    // Select SUV
    cy.contains(mockVehicleTypeOptions.array[1].label).click()

    // Verify selection
    getInputInsideByTestId(vehicleTypeDataTestId).should(
      'have.value',
      mockVehicleTypeOptions.array[1].label,
    )

    // Fill number of passengers
    getInputInsideByTestId(numberOfPassengersDataTestId).type('3')

    cy.log(
      '/**************************************** Driver Section ****************************************/',
    )

    // Check driver section
    cy.findByTestId(driverSelectionDataTestId + '-any').should('be.visible')
    cy.findByTestId(driverSelectionDataTestId + '-self-drive').should('be.visible')
    cy.findByTestId(driverSelectionDataTestId + '-specific').should('be.visible')

    // Driver selection should default to "Any"
    getInputInsideByTestId(driverSelectionDataTestId + '-any').should('be.checked')

    // Initially, driver autocomplete should not be visible
    cy.findByTestId(driverSelectDataTestId).should('not.exist')

    cy.log('/***********Test Self-Drive Selection **********/')

    // Test self-drive selection
    cy.findByTestId(driverSelectionDataTestId + '-self-drive').click()
    getInputInsideByTestId(driverSelectionDataTestId + '-self-drive').should(
      'be.checked',
    )
    getInputInsideByTestId(driverSelectionDataTestId + '-any').should('not.be.checked')

    // Trigger validation
    cy.findByTestId('StepThree-NextButton').click()

    // Verify form is valid and check submitted values
    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.driverSelection).to.equal('self-drive')
      expect(submittedData?.driverId).to.equal(null)
    })

    cy.log('/***********Test Specific Driver Selection **********/')

    // Test specific driver selection
    cy.findByTestId(driverSelectionDataTestId + '-specific').click()
    getInputInsideByTestId(driverSelectionDataTestId + '-specific').should('be.checked')

    // Driver autocomplete should now be visible
    cy.findByTestId(driverSelectDataTestId).should('be.visible')

    // Trigger validation without selecting a driver - should fail
    cy.findByTestId('StepThree-NextButton').click()

    cy.then(() => {
      expect(isFormValid).to.equal(false)
    })

    // Select a specific driver
    cy.findByTestId(driverSelectDataTestId).click()
    cy.contains(mockParsedDriverData[0].name).click()

    // Trigger validation again - should pass
    cy.findByTestId('StepThree-NextButton').click()

    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.driverSelection).to.equal('specific')
      expect(submittedData?.driverId).to.equal(mockParsedDriverData[0].id)
    })

    cy.log('/********** Test Any Driver Selection **********/')

    // Switch back to "Any" driver
    cy.findByTestId(driverSelectionDataTestId + '-any').click()
    getInputInsideByTestId(driverSelectionDataTestId + '-any').should('be.checked')
    cy.findByTestId(driverSelectDataTestId).should('not.exist')

    // Trigger validation - should pass
    cy.findByTestId('StepThree-NextButton').click()

    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.driverSelection).to.equal('any')
      expect(submittedData?.driverId).to.equal(null)
    })

    // Switch back to "Any" for simplicity
    cy.findByTestId(driverSelectionDataTestId + '-any').click()
    getInputInsideByTestId(driverSelectionDataTestId + '-any').should('be.checked')
    cy.findByTestId(driverSelectDataTestId).should('not.exist')

    cy.log(
      '/**************************************** Vehicle Commander Section ****************************************/',
    )

    // Check vehicle commander section
    cy.findByTestId(vehicleCommanderTitleDataTestId).should('be.visible')

    // Vehicle commander selection should default to "Any"
    getInputInsideByTestId(vehicleCommanderSelectionDataTestId + '-any').should(
      'be.checked',
    )

    // Initially, user autocomplete should not be visible
    cy.findByTestId(vehicleCommanderSelectDataTestId).should('not.exist')

    cy.log('/***********Test Self-Command Selection **********/')

    // Test self-command selection
    cy.findByTestId('StepThree-VehicleCommanderSelection-self-command').click()
    getInputInsideByTestId('StepThree-VehicleCommanderSelection-self-command').should(
      'be.checked',
    )

    // Trigger validation
    cy.findByTestId('StepThree-NextButton').click()

    // Verify form is valid and check submitted values
    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.vehicleCommanderSelection).to.equal('self-command')
      expect(submittedData?.vehicleCommanderId).to.equal(null)
    })

    cy.log('/***********Test Specific Commander Selection **********/')

    // Test specific commander selection
    cy.findByTestId('StepThree-VehicleCommanderSelection-specific').click()
    getInputInsideByTestId('StepThree-VehicleCommanderSelection-specific').should(
      'be.checked',
    )

    // User autocomplete should now be visible
    cy.findByTestId(vehicleCommanderSelectDataTestId).should('be.visible')

    // Trigger validation without selecting a commander - should fail
    cy.findByTestId('StepThree-NextButton').click()

    cy.then(() => {
      expect(isFormValid).to.equal(false)
    })

    // Select a specific commander
    cy.findByTestId(vehicleCommanderSelectDataTestId).click()
    cy.contains(duxsMocks.admin.users[1].username).click()

    // Trigger validation again - should pass
    cy.findByTestId('StepThree-NextButton').click()

    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.vehicleCommanderSelection).to.equal('specific')
      expect(submittedData?.vehicleCommanderId).to.equal(duxsMocks.admin.users[1].id)
    })

    cy.log('/*********Test Any Commander Selection *********/')

    // Switch back to "Any" commander
    cy.findByTestId(vehicleCommanderSelectionDataTestId + '-any').click()
    getInputInsideByTestId(vehicleCommanderSelectionDataTestId + '-any').should(
      'be.checked',
    )
    cy.findByTestId(vehicleCommanderSelectDataTestId).should('not.exist')

    // Trigger validation - should pass
    cy.findByTestId('StepThree-NextButton').click()

    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.vehicleCommanderSelection).to.equal('any')
      expect(submittedData?.vehicleCommanderId).to.equal(null)
    })

    cy.log(
      '/**************************************** Equipment Section ****************************************/',
    )

    // Check equipment section
    cy.findByTestId('StepThree-EquipmentsTitle').should('be.visible')
    cy.contains('No equipment').should('be.visible')

    // Equipment type should default to "No equipment"
    getInputInsideByTestId(equipmentTypeDataTestId + '-no-equipment').should(
      'be.checked',
    )

    // Initially, equipment autocomplete should not be visible
    cy.findByTestId(equipmentSelectDataTestId).should('not.exist')

    // Test car boot selection
    cy.findByTestId('StepThree-EquipmentType-car-boot').click()
    getInputInsideByTestId('StepThree-EquipmentType-car-boot').should('be.checked')
    getInputInsideByTestId('StepThree-EquipmentType-no-equipment').should(
      'not.be.checked',
    )

    // Equipment autocomplete should now be visible
    cy.findByTestId(equipmentSelectDataTestId).should('be.visible')

    // Test larger space selection
    cy.findByTestId('StepThree-EquipmentType-larger-space').click()
    getInputInsideByTestId('StepThree-EquipmentType-larger-space').should('be.checked')
    getInputInsideByTestId('StepThree-EquipmentType-car-boot').should('not.be.checked')

    // Switch back to "No equipment" for simplicity
    cy.findByTestId(equipmentTypeDataTestId + '-no-equipment').click()
    getInputInsideByTestId(equipmentTypeDataTestId + '-no-equipment').should(
      'be.checked',
    )

    // Equipment fields should be hidden
    cy.findByTestId(equipmentSelectDataTestId).should('not.exist')

    cy.log(
      '/**************************************** Remarks Section ****************************************/',
    )

    // Check remarks section
    cy.findByTestId('StepThree-RemarksTitle').should('be.visible')
    cy.findByTestId(remarksInputDataTestId).should('be.visible')

    // Fill remarks
    const testRemarks = 'This is a test remark for the booking'
    getInputInsideByTestId(remarksInputDataTestId).type(testRemarks)
    getInputInsideByTestId(remarksInputDataTestId).should('have.value', testRemarks)

    cy.log(
      '/**************************************** Final Validation ****************************************/',
    )

    // Trigger validation - should pass now
    cy.findByTestId('StepThree-NextButton').click()

    // No validation errors should be present
    cy.findByTestId(vehicleTypeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    cy.findByTestId(numberOfPassengersDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    // Verify form is valid and check submitted values
    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData).to.not.equal(null)
      expect(submittedData?.vehicleTypeId).to.equal(mockVehicleTypeOptions.array[1].id)
      expect(submittedData?.numberOfPassengers).to.equal(3)
      expect(submittedData?.driverSelection).to.equal('any')
      expect(submittedData?.vehicleCommanderSelection).to.equal('any')
      expect(submittedData?.equipmentType).to.equal('no-equipment')
      expect(submittedData?.remarks).to.equal(testRemarks)
    })
  })

  it('should disable vehicle type when key is collected', () => {
    mountStepThree({ isKeyCollected: true })

    getInputInsideByTestId(vehicleTypeDataTestId).should('be.disabled')
  })

  it('should show vehicle registration field when vehicleIdExists is true', () => {
    mountStepThree({ vehicleIdExists: true })

    cy.findByTestId(vehicleIdDataTestId).should('exist')
  })

  it('should hide driver selection in approve mode', () => {
    mountStepThree({ mode: 'approve' })

    // Driver selection radio buttons should not be visible
    cy.findByTestId(driverSelectionDataTestId + '-any').should('not.exist')
    cy.findByTestId(driverSelectionDataTestId + '-self-drive').should('not.exist')
    cy.findByTestId(driverSelectionDataTestId + '-specific').should('not.exist')
  })

  it('should show info alert for single and return journey types', () => {
    mountStepThree({ initialValues: { journeyType: JOURNEY_TYPE.SINGLE } })

    cy.findByTestId('StepThree-VehicleCommanderAlert').should('exist')
  })

  it('should clear equipment data when switching to "No equipment"', () => {
    mountStepThree({
      initialValues: {
        equipmentType: EQUIPMENT_TYPE.CAR_BOOT,
        equipmentIds: [1 as any], // Type assertion for test purposes
      },
    })

    // Switch to "No equipment"
    cy.findByLabelText('No equipment').click()

    // Equipment fields should be hidden
    cy.findByTestId(equipmentSelectDataTestId).should('not.exist')
  })

  it('should disable all form fields when disabled prop is true', () => {
    mountStepThree({ disabled: true })

    // All interactive fields should be disabled
    getInputInsideByTestId(vehicleTypeDataTestId).should('be.disabled')
    getInputInsideByTestId(numberOfPassengersDataTestId).should('be.disabled')
    getInputInsideByTestId(driverSelectionDataTestId + '-any').should('be.disabled')
    getInputInsideByTestId(driverSelectionDataTestId + '-self-drive').should(
      'be.disabled',
    )
    getInputInsideByTestId(driverSelectionDataTestId + '-specific').should(
      'be.disabled',
    )
    getInputInsideByTestId(remarksInputDataTestId).should('be.disabled')
  })

  it('should test image upload functionality comprehensively', () => {
    let submittedData: BookingFormSchema | null = null
    let isFormValid = false

    // Test with initial uploaded images
    const initialUploadedImages = [
      {
        guid: 'test-guid-1',
        extension: 'jpg',
        fileName: 'initial-image-1.jpg',
        contentType: 'image/jpeg',
        previewUrl: 'blob:http://localhost:3000/test-preview-1',
      },
      {
        guid: 'test-guid-2',
        extension: 'png',
        fileName: 'initial-image-2.png',
        contentType: 'image/png',
        previewUrl: 'blob:http://localhost:3000/test-preview-2',
      },
    ]

    mountStepThree({
      initialValues: {
        equipmentType: 'car-boot',
        uploadedImages: initialUploadedImages,
      },
      onFormSubmit: (data, valid) => {
        submittedData = data
        isFormValid = valid
      },
    })

    // Fill required fields first
    cy.findByTestId(vehicleTypeDataTestId).click()
    cy.contains(mockVehicleTypeOptions.array[0].label).click()
    getInputInsideByTestId(numberOfPassengersDataTestId).type('2')

    // Equipment type should be pre-selected as car-boot
    getInputInsideByTestId('StepThree-EquipmentType-car-boot').should('be.checked')

    // Equipment autocomplete should be visible
    cy.findByTestId(equipmentSelectDataTestId).should('be.visible')

    /**************************************** Test Initial Images Display ****************************************/

    // Check that initial images are displayed by looking for file names
    cy.findByTestId(
      uploadedImageDataTestIdPrefix +
        initialUploadedImages[0].fileName +
        uploadedImageFileNameSuffix,
    ).should('exist')
    cy.findByTestId(
      uploadedImageDataTestIdPrefix +
        initialUploadedImages[1].fileName +
        uploadedImageFileNameSuffix,
    ).should('exist')

    // Test view functionality for initial images - look for view buttons (VisibilityIcon)
    cy.findByTestId(
      uploadedImageDataTestIdPrefix + initialUploadedImages[0].fileName + '-ViewButton',
    ).click()
    cy.findByTestId(previewImageModalTestId).should('be.visible')
    cy.findByTestId(previewImageModalTestId + '-CloseButton').click()
    cy.findByTestId(previewImageModalTestId).should('not.exist')

    /**************************************** Test Image Removal ****************************************/

    // Test remove functionality for initial images - look for remove buttons (CloseIcon)
    cy.findByTestId(
      uploadedImageDataTestIdPrefix +
        initialUploadedImages[0].fileName +
        '-RemoveButton',
    ).click()
    cy.findByTestId(
      uploadedImageDataTestIdPrefix +
        initialUploadedImages[0].fileName +
        uploadedImageFileNameSuffix,
    ).should('not.exist')
    cy.findByTestId(
      uploadedImageDataTestIdPrefix +
        initialUploadedImages[1].fileName +
        uploadedImageFileNameSuffix,
    ).should('be.visible')

    /**************************************** Test Image Upload ****************************************/

    // Create a test file for upload
    const testFileName = 'test-upload.jpg'
    const testFileContent = 'test file content'

    // Mock the file upload
    cy.findByTestId('ImageUploader-DropzoneInput').selectFile(
      {
        contents: Cypress.Buffer.from(testFileContent),
        fileName: testFileName,
        mimeType: 'image/jpeg',
      },
      { force: true },
    )

    // Wait for upload to complete and check that new image is added
    cy.findByTestId(
      uploadedImageDataTestIdPrefix + testFileName + uploadedImageFileNameSuffix,
    ).should('be.visible')

    /**************************************** Test Equipment Selection with Images ****************************************/

    // Select some equipment
    cy.findByTestId(equipmentSelectDataTestId).click()
    cy.findByTestId(equipmentOptionDataTestIdPrefix + mockEquipmentData[0].id).click()
    cy.findByTestId(equipmentOptionDataTestIdPrefix + mockEquipmentData[1].id).click()

    // Click outside to close the dropdown
    cy.get('body').click()

    /**************************************** Test Form Submission with Images ****************************************/

    // Trigger validation - should pass
    cy.findByTestId('StepThree-NextButton').click()

    // Verify form is valid and check submitted values including images
    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.equipmentType).to.equal('car-boot')
      expect(submittedData?.equipmentIds).to.have.length(2)
      expect(submittedData?.equipmentIds).to.include(1) // Laptop
      expect(submittedData?.equipmentIds).to.include(2) // Projector
      expect(submittedData?.uploadedImages).to.have.length(2)
      expect(submittedData?.uploadedImages[0].fileName).to.equal('initial-image-2.png')
      expect(submittedData?.uploadedImages[1].fileName).to.equal(testFileName)
    })

    /**************************************** Test Switching Equipment Type Clears Images ****************************************/

    // Switch to "No equipment" - should clear equipment and images
    cy.findByTestId('StepThree-EquipmentType-no-equipment').click()
    getInputInsideByTestId('StepThree-EquipmentType-no-equipment').should('be.checked')

    // Equipment fields should be hidden
    cy.findByTestId(equipmentSelectDataTestId).should('not.exist')

    // Trigger validation again
    cy.findByTestId('StepThree-NextButton').click()

    // Verify equipment data is cleared but images remain
    cy.then(() => {
      expect(isFormValid).to.equal(true)
      expect(submittedData?.equipmentType).to.equal('no-equipment')
      expect(submittedData?.equipmentIds).to.have.length(0)
      // Images should be automatically cleared
      expect(submittedData?.uploadedImages).to.have.length(0)
    })
  })

  it('should test image upload with file validation', () => {
    mountStepThree({
      initialValues: {
        equipmentType: 'larger-space',
      },
    })

    // Fill required fields first
    cy.findByTestId(vehicleTypeDataTestId).click()
    cy.contains(mockVehicleTypeOptions.array[0].label).click()
    getInputInsideByTestId(numberOfPassengersDataTestId).type('2')

    /**************************************** Test Invalid File Type ****************************************/

    // Try to upload an invalid file type
    const invalidFileName = 'test-document.txt'
    const invalidFileContent = 'This is a text document'

    cy.findByTestId('ImageUploader-DropzoneInput').selectFile(
      {
        contents: Cypress.Buffer.from(invalidFileContent),
        fileName: invalidFileName,
        mimeType: 'text/plain',
      },
      { force: true },
    )

    // Should show error message for invalid file type
    cy.contains('File type not supported: test-document.txt').should('be.visible')

    /**************************************** Test File Size Limit ****************************************/

    // Create a large file (over 3MB)
    const largeFileName = 'large-image.jpg'
    const largeFileContent = 'x'.repeat(4 * 1024 * 1024) // 4MB

    cy.findByTestId('ImageUploader-DropzoneInput').selectFile(
      {
        contents: Cypress.Buffer.from(largeFileContent),
        fileName: largeFileName,
        mimeType: 'image/jpeg',
      },
      { force: true },
    )

    // Should show error message for file size
    cy.contains('File is too large (max 3MB): large-image.jpg').should('be.visible')

    /**************************************** Test Valid File Upload ****************************************/

    // Upload a valid file
    const validFileName = 'valid-image.png'
    const validFileContent = 'valid image content'

    cy.findByTestId('ImageUploader-DropzoneInput').selectFile(
      {
        contents: Cypress.Buffer.from(validFileContent),
        fileName: validFileName,
        mimeType: 'image/png',
      },
      { force: true },
    )

    // Should successfully upload
    cy.findByTestId(
      uploadedImageDataTestIdPrefix + validFileName + uploadedImageFileNameSuffix,
    ).should('exist')

    /**************************************** Test Maximum Files Limit ****************************************/

    // Try to upload more files to test the 5-file limit
    for (let i = 2; i <= 6; i++) {
      const fileName = `test-image-${i}.jpg`
      cy.findByTestId('ImageUploader-DropzoneInput').selectFile(
        {
          contents: Cypress.Buffer.from(`content ${i}`),
          fileName,
          mimeType: 'image/jpeg',
        },
        { force: true },
      )

      if (i <= 5) {
        cy.findByTestId(
          uploadedImageDataTestIdPrefix + fileName + uploadedImageFileNameSuffix,
        ).should('exist')
      }
    }

    // Should show warning about maximum files if limit exceeded
    cy.contains('Maximum 5 files allowed').should('be.visible')
  })
})
