/* eslint-disable no-param-reassign */
/// <reference types="@testing-library/cypress" />
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { match } from 'ts-pattern'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import {
  BOOKING_PURPOSE_OFFICIAL_BUSINESS,
  carpoolEndpointMocks,
  DRIVER_JOHN_DOE,
  REJECT_REASON_VEHICLE_NOT_AVAILABLE,
  TEST_BOOKING_DESCRIPTION,
  TEST_REMARKS,
  TEST_REQUESTOR,
  VEHICLE_ABC123,
} from 'src/cypress-ct/mocks/endpoints/carpool'
import { exhaustiveEndpointCallCheck, mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import type { RejectBookingFormData } from './types'
import ViewApproveBookingDrawer from './ViewApproveBookingDrawer'

// Test wrapper component
function TestViewApproveBookingDrawer({
  mode = 'view',
  onClose = cy.stub(),
  onSubmit = cy.stub(),
  onReject = cy.stub(),
  onApprovalLoading = false,
  onRejectLoading = false,
  bookingId = '123',
}: {
  mode?: 'view' | 'approve'
  onClose?: () => void
  onSubmit?: (values: any) => void
  onReject?: (data: RejectBookingFormData) => void
  onApprovalLoading?: boolean
  onRejectLoading?: boolean
  bookingId?: string
}) {
  const props = {
    onClose,
    bookingId,
    mode,
    ...(mode === 'approve'
      ? {
          onSubmit,
          onReject,
          onApprovalLoading,
          onRejectLoading,
        }
      : {}),
  }

  return <ViewApproveBookingDrawer {...(props as any)} />
}

// Handle base Cypress endpoints for ViewApproveBookingDrawer
const handleBaseCyEndpoints = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_booking_details' }, () => {
      req.reply(carpoolEndpointMocks.ct_fleet_get_booking_details())
    })
    .with({ method: 'ct_fleet_get_booking_attachments' }, () => {
      req.reply(carpoolEndpointMocks.ct_fleet_get_booking_attachments())
    })
    .with({ method: 'ct_fleet_get_booking_options' }, () => {
      req.reply(carpoolEndpointMocks.ct_fleet_get_booking_options())
    })
    .with({ method: 'ct_fleet_get_users' }, () => {
      req.reply(carpoolEndpointMocks.ct_fleet_get_users())
    })
    .with({ method: 'ct_fleet_get_carpool_booking_time_rules' }, () => {
      req.reply(carpoolEndpointMocks.ct_fleet_get_carpool_booking_time_rules())
    })
    .with({ method: 'ct_fleet_approve_booking' }, () => {
      req.alias = 'approveBooking'
      req.reply(carpoolEndpointMocks.ct_fleet_approve_booking())
    })
    .with({ method: 'ct_fleet_reject_booking' }, () => {
      req.alias = 'rejectBooking'
      req.reply(carpoolEndpointMocks.ct_fleet_reject_booking())
    })
    .otherwise(exhaustiveEndpointCallCheck)

const mountViewApproveBookingDrawer = (
  props?: Parameters<typeof TestViewApproveBookingDrawer>[0],
) => {
  mountWithProviders(<TestViewApproveBookingDrawer {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user({
          carpoolApproveBookings: true,
          carpoolDeclineBookings: true,
        }).mockState,
      },
    },
  })
}

describe('ViewApproveBookingDrawer Component', () => {
  it('View Mode - should render correctly in view mode', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      handleBaseCyEndpoints(req)
    })
    mountViewApproveBookingDrawer({ mode: 'view' })

    // Check header title
    cy.findByTestId('BookingDrawerLayout-Title').should('contain.text', 'View Booking')

    // Check booking ID is displayed
    cy.findByTestId('BookingDrawerLayout-Title').parent().should('contain.text', '123')

    // Check all form sections are visible but disabled
    cy.findByTestId('StepOne-Title').should('be.visible')
    cy.findByTestId('StepTwo-PickupTime').should('be.visible')
    cy.findByTestId('StepTwo-DropoffTime').should('be.visible')
    cy.findByTestId('StepThree-VehicleType').should('be.visible')
    cy.findByTestId('StepThree-DriverSelection').should('be.visible')
    cy.findByTestId('StepThree-VehicleCommanderTitle').should('be.visible')
    cy.findByTestId('StepThree-EquipmentsTitle').should('be.visible')
    cy.findByTestId('StepThree-RemarksTitle').should('be.visible')

    // All fields should be disabled
    cy.findByTestId('StepOne-PurposeOfRequest').should('be.disabled')
    cy.findByTestId('StepOne-RequestDescription').should('be.disabled')

    // Only Cancel button should be visible
    cy.findByTestId('ViewApproveBookingDrawer-Cancel').should('be.visible')
    cy.findByTestId('ViewApproveBookingDrawer-Approve').should('not.exist')
    cy.findByTestId('ViewApproveBookingDrawer-Reject').should('not.exist')

    // Check that form fields are populated with booking data
    cy.findByTestId('StepOne-PurposeOfRequest').should(
      'contain.text',
      BOOKING_PURPOSE_OFFICIAL_BUSINESS,
    )
    cy.findByTestId('StepOne-RequestDescription').should(
      'have.value',
      TEST_BOOKING_DESCRIPTION,
    )
    cy.findByTestId('StepOne-Requestor').should('have.value', TEST_REQUESTOR)
    cy.findByTestId('RemarksInput').find('input').should('have.value', TEST_REMARKS)
  })

  describe('Approve Mode', () => {
    it('should render correctly in approve mode and call onSubmit with valid form', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })
      const onSubmit = cy.stub().as('onSubmit')
      mountViewApproveBookingDrawer({
        mode: 'approve',
        onSubmit,
      })

      // Check header title
      cy.findByTestId('BookingDrawerLayout-Title').should(
        'contain.text',
        'Approve/Reject Booking',
      )

      // Check action buttons
      cy.findByTestId('ViewApproveBookingDrawer-Cancel').should('be.visible')
      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('be.visible')
      cy.findByTestId('ViewApproveBookingDrawer-Reject').should('be.visible')

      // Form fields should be enabled for editing
      cy.findByTestId('StepThree-VehicleId').should('not.be.disabled')
      cy.findByTestId('StepThree-DriverSelect').should('not.be.disabled')
      cy.findByTestId('StepThree-VehicleCommanderSelect').should('not.be.disabled')

      // Driver selection should be set to "Specific" and show driver field
      cy.findByTestId('StepThree-DriverSelect').should('be.visible')

      // Vehicle commander selection should be set to "Specific" and show commander field
      cy.findByTestId('StepThree-VehicleCommanderSelect').should('be.visible')

      // Should show vehicle registration field
      cy.findByTestId('StepThree-VehicleId').should('be.visible')

      // Should not show vehicle type field
      cy.findByTestId('StepThree-VehicleType').should('not.exist')

      // Fill the vehicle registration field to enable approve button
      cy.findByTestId('StepThree-VehicleId').click()
      cy.findByText(VEHICLE_ABC123).click()

      // Fill driver field
      cy.findByTestId('StepThree-DriverSelect').click()
      cy.findByText(DRIVER_JOHN_DOE).click()

      // Fill vehicle commander field
      cy.findByTestId('StepThree-VehicleCommanderSelect').click()
      cy.findByText('john.doe').click()

      // Now approve button should be enabled
      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('not.be.disabled')
      cy.findByTestId('ViewApproveBookingDrawer-Approve').click()

      cy.wait('@approveBooking')
      cy.get('@onSubmit').should('have.been.called')
    })

    it('should show loading state on Approve button during submission', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })
      mountViewApproveBookingDrawer({
        mode: 'approve',
        onApprovalLoading: true,
      })

      // Approve button should show loading state
      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('be.disabled')
      cy.get('.MuiCircularProgress-root').should('be.visible')
    })
  })

  describe('Rejection Functionality', () => {
    it('should open reject modal when Reject button is clicked', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })
      const onReject = cy.stub().as('onReject')
      mountViewApproveBookingDrawer({ mode: 'approve', onReject })

      cy.findByTestId('ViewApproveBookingDrawer-Reject').click()

      // Reject modal should be visible
      cy.findByTestId('RejectBookingModal').should('be.visible')

      // Fill reject form and confirm
      cy.findByTestId('RejectBookingModal-RejectReason').click()
      cy.findByText(REJECT_REASON_VEHICLE_NOT_AVAILABLE).click()
      cy.findByTestId('RejectBookingModal-Remarks').type(
        'Not available for this time slot',
      )
      cy.findByTestId('ConfirmationModal-confirm-button').click()

      cy.wait('@rejectBooking')
      cy.get('@onReject').should('have.been.called')
    })

    it('should close reject modal when cancelled', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })
      mountViewApproveBookingDrawer({ mode: 'approve' })

      cy.findByTestId('ViewApproveBookingDrawer-Reject').click()

      // Reject modal should be visible
      cy.findByTestId('RejectBookingModal').should('be.visible')

      // Cancel the rejection
      cy.findByTestId('ConfirmationModal-cancel-button').click()

      // Modal should be closed
      cy.findByTestId('RejectBookingModal').should('not.exist')
    })
  })

  describe('Permission-based Rendering', () => {
    it('should hide Approve button when user lacks approval permission', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })

      mountWithProviders(<TestViewApproveBookingDrawer mode="approve" />, {
        reduxOptions: {
          preloadedState: {
            user: duxsMocks.user({
              carpoolApproveBookings: false,
              carpoolDeclineBookings: true,
            }).mockState,
          },
        },
      })

      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('not.exist')
      cy.findByTestId('ViewApproveBookingDrawer-Reject').should('be.visible')
    })

    it('should hide Reject button when user lacks decline permission', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })

      mountWithProviders(<TestViewApproveBookingDrawer mode="approve" />, {
        reduxOptions: {
          preloadedState: {
            user: duxsMocks.user({
              carpoolApproveBookings: true,
              carpoolDeclineBookings: false,
            }).mockState,
          },
        },
      })

      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('be.visible')
      cy.findByTestId('ViewApproveBookingDrawer-Reject').should('not.exist')
    })
  })

  describe('Close Functionality', () => {
    it('should call onClose when Cancel button is clicked', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })
      const onClose = cy.stub().as('onClose')
      mountViewApproveBookingDrawer({ mode: 'view', onClose })

      cy.findByTestId('ViewApproveBookingDrawer-Cancel').click()
      cy.get('@onClose').should('have.been.called')
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      // Mock API error
      cy.intercept('POST', '/jsonrpc/public/index.php', {
        statusCode: 500,
        body: { error: 'Internal server error' },
      })

      const onClose = cy.stub().as('onClose')
      mountViewApproveBookingDrawer({ onClose })

      // Should call onClose when there's an error
      cy.get('@onClose').should('have.been.called')
    })
  })

  describe('Form Validation in Approve Mode', () => {
    it('should validate required fields in approve mode', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        handleBaseCyEndpoints(req)
      })
      mountViewApproveBookingDrawer({ mode: 'approve' })

      // Clear required fields
      cy.findByLabelText(/Vehicle registrations/i).clear()
      cy.findByLabelText(/Select Driver/i).clear()

      // Try to approve - should be disabled
      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('be.disabled')

      // Should show validation errors
      cy.findByText(messages.required).should('be.visible')
    })

    it('should submit correct values when approving booking', () => {
      let submittedValues: any = null
      const onSubmit = cy
        .stub()
        .callsFake((values) => {
          submittedValues = values
        })
        .as('onSubmit')

      // Mock the approve API to capture submitted values
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        match(req.body)
          .with({ method: 'ct_fleet_approve_booking' }, () => {
            // Validate the submitted values
            expect(submittedValues).to.not.equal(null)
            expect(submittedValues.vehicleId).to.equal(1) // ABC123 vehicle ID
            expect(submittedValues.driverId).to.equal(1) // John Doe driver ID
            expect(submittedValues.vehicleCommanderId).to.equal('john.doe')
            expect(submittedValues.requestClientUserId).to.equal('')

            req.alias = 'approveBooking'
            req.reply(carpoolEndpointMocks.ct_fleet_approve_booking())
          })
          .otherwise(() => handleBaseCyEndpoints(req))
      })

      mountViewApproveBookingDrawer({
        mode: 'approve',
        onSubmit,
      })

      // Fill required fields
      cy.findByTestId('StepThree-VehicleId').click()
      cy.findByText(VEHICLE_ABC123).click()

      cy.findByTestId('StepThree-DriverSelect').click()
      cy.findByText(DRIVER_JOHN_DOE).click()

      cy.findByTestId('StepThree-VehicleCommanderSelect').click()
      cy.findByText('john.doe').click()

      // Submit the form
      cy.findByTestId('ViewApproveBookingDrawer-Approve').click()

      cy.wait('@approveBooking')
      cy.get('@onSubmit').should('have.been.called')
    })

    it('should submit correct values when rejecting booking', () => {
      let submittedValues: any = null
      const onReject = cy
        .stub()
        .callsFake((values) => {
          submittedValues = values
        })
        .as('onReject')

      // Mock the reject API to capture submitted values
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        match(req.body)
          .with({ method: 'ct_fleet_reject_booking' }, () => {
            // Validate the submitted values
            expect(submittedValues).to.not.equal(null)
            expect(submittedValues.reasonId).to.equal(1) // Vehicle not available reason ID
            expect(submittedValues.remarks).to.equal('Not available for this time slot')

            req.alias = 'rejectBooking'
            req.reply(carpoolEndpointMocks.ct_fleet_reject_booking())
          })
          .otherwise(() => handleBaseCyEndpoints(req))
      })

      mountViewApproveBookingDrawer({
        mode: 'approve',
        onReject,
      })

      // Open reject modal
      cy.findByTestId('ViewApproveBookingDrawer-Reject').click()

      // Fill reject form
      cy.findByTestId('RejectBookingModal-RejectReason').click()
      cy.findByText(REJECT_REASON_VEHICLE_NOT_AVAILABLE).click()
      cy.findByTestId('RejectBookingModal-Remarks').type(
        'Not available for this time slot',
      )

      // Submit rejection
      cy.findByTestId('ConfirmationModal-confirm-button').click()

      cy.wait('@rejectBooking')
      cy.get('@onReject').should('have.been.called')
    })
  })
})
