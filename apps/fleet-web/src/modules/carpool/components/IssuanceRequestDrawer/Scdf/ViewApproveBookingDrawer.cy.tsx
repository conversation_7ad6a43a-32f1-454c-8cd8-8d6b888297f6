/// <reference types="@testing-library/cypress" />
import { DateTime } from 'luxon'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import type { RejectBookingFormData } from './types'
import ViewApproveBookingDrawer from './ViewApproveBookingDrawer'

// Mock booking data
const mockBookingDetails = {
  id: '123',
  statusId: 1,
  keyReturnTs: null,
  purposeOfRequestId: 1,
  requestDescription: 'Test booking description',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: 1,
  vehicleId: 1,
  numberOfPassengers: 2,
  driverSelection: 'specific',
  driverId: 1,
  vehicleCommanderSelection: 'specific',
  vehicleCommanderId: 'user123',
  pickupTime: DateTime.now().plus({ hours: 2 }).toISO(),
  pickupLocationId: 1,
  dropoffTime: DateTime.now().plus({ hours: 4 }).toISO(),
  journeyType: 'return',
  journeys: [],
  equipmentType: 'no-equipment',
  equipmentIds: [],
  remarks: 'Test remarks',
}

// Test wrapper component
function TestViewApproveBookingDrawer({
  mode = 'view',
  onClose = cy.stub(),
  onSubmit = cy.stub(),
  onReject = cy.stub(),
  onApprovalLoading = false,
  onRejectLoading = false,
  bookingId = '123',
}: {
  mode?: 'view' | 'approve'
  onClose?: () => void
  onSubmit?: (values: any) => void
  onReject?: (data: RejectBookingFormData) => void
  onApprovalLoading?: boolean
  onRejectLoading?: boolean
  bookingId?: string
}) {
  const props = {
    onClose,
    bookingId,
    mode,
    ...(mode === 'approve'
      ? {
          onSubmit,
          onReject,
          onApprovalLoading,
          onRejectLoading,
        }
      : {}),
  }

  return <ViewApproveBookingDrawer {...(props as any)} />
}

const mountViewApproveBookingDrawer = (
  props?: Parameters<typeof TestViewApproveBookingDrawer>[0],
) => {
  // Mock API calls
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    if (req.body.method === 'ct_fleet_get_booking_details') {
      req.reply({
        body: {
          id: 10,
          result: mockBookingDetails,
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_booking_attachments') {
      req.reply({
        body: {
          id: 10,
          result: [],
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_booking_options') {
      req.reply({
        body: {
          id: 10,
          result: {
            bookingPurposes: [
              { id: 1, name: 'Official Business' },
              { id: 2, name: 'Training' },
            ],
            locations: [
              { id: 1, name: 'Main Office' },
              { id: 2, name: 'Branch Office' },
            ],
            vehicleTypes: [
              { id: 1, name: 'Sedan' },
              { id: 2, name: 'SUV' },
            ],
            rejectReasons: [
              { id: 1, name: 'Vehicle not available' },
              { id: 2, name: 'Driver not available' },
            ],
          },
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_users') {
      req.reply({
        body: {
          id: 10,
          result: [
            { id: 1, username: 'john.doe', email: '<EMAIL>' },
            { id: 2, username: 'jane.smith', email: '<EMAIL>' },
          ],
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_carpool_booking_time_rules') {
      req.reply({
        body: {
          id: 10,
          result: {
            carpoolBookingInAdvance: 24,
            carpoolBookingInAdvanceUnit: 'hours',
            carpoolMaximumBookingTime: 8,
            carpoolMaximumBookingTimeUnit: 'hours',
          },
        },
      })
    }
  }).as('getBookingData')

  mountWithProviders(<TestViewApproveBookingDrawer {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user({
          carpoolApproveBookings: true,
          carpoolDeclineBookings: true,
        }).mockState,
      },
    },
  })
}

describe('ViewApproveBookingDrawer Component', () => {
  describe('View Mode', () => {
    it('should render correctly in view mode', () => {
      mountViewApproveBookingDrawer({ mode: 'view' })

      cy.wait('@getBookingData')

      // Check header title
      cy.findByTestId('BookingDrawerLayout-Title').should(
        'contain.text',
        'View Booking',
      )

      // Check booking ID is displayed
      cy.findByTestId('BookingDrawerLayout-Title')
        .parent()
        .should('contain.text', '123')

      // Check all form sections are visible but disabled
      cy.findByTestId('StepOne-Title').should('be.visible')
      cy.findByTestId('StepTwo-PickupTime').should('be.visible')
      cy.findByTestId('StepTwo-DropoffTime').should('be.visible')
      cy.findByTestId('StepThree-VehicleType').should('be.visible')
      cy.findByTestId('StepThree-DriverSelection').should('be.visible')
      cy.findByTestId('StepThree-VehicleCommanderTitle').should('be.visible')
      cy.findByTestId('StepThree-EquipmentsTitle').should('be.visible')
      cy.findByTestId('StepThree-RemarksTitle').should('be.visible')

      // All fields should be disabled
      cy.findByTestId('StepOne-PurposeOfRequest').should('be.disabled')
      cy.findByTestId('StepOne-RequestDescription').should('be.disabled')

      // Only Cancel button should be visible
      cy.findByTestId('ViewApproveBookingDrawer-Cancel').should('be.visible')
      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('not.exist')
      cy.findByTestId('ViewApproveBookingDrawer-Reject').should('not.exist')

      // Check that form fields are populated with booking data
      cy.get('[data-testid="StepOne-PurposeOfRequest"]').should(
        'contain.text',
        'Official Business',
      )
      cy.get('[data-testid="StepOne-RequestDescription"]').should(
        'have.value',
        'Test booking description',
      )
      cy.get('[data-testid="StepOne-Requestor"]').should('have.value', 'test.user')
      cy.get('[data-testid="RemarksInput"]')
        .find('input')
        .should('have.value', 'Test remarks')
    })
  })

  describe('Approve Mode', () => {
    it('should render correctly in approve mode and call onSubmit with valid form', () => {
      const onSubmit = cy.stub().as('onSubmit')
      mountViewApproveBookingDrawer({
        mode: 'approve',
        onSubmit,
      })

      cy.wait('@getBookingData')

      // Check header title
      cy.findByTestId('BookingDrawerLayout-Title').should(
        'contain.text',
        'Approve/Reject Booking',
      )

      // Check action buttons
      cy.findByTestId('ViewApproveBookingDrawer-Cancel').should('be.visible')
      cy.findByTestId('ViewApproveBookingDrawer-Approve').should('be.visible')
      cy.findByTestId('ViewApproveBookingDrawer-Reject').should('be.visible')

      // Form fields should be enabled for editing
      cy.get('[data-testid="StepThree-VehicleId"]').should('not.be.disabled')
      cy.get('[data-testid="StepThree-DriverSelect"]').should('not.be.disabled')
      cy.get('[data-testid="StepThree-VehicleCommanderSelect"]').should(
        'not.be.disabled',
      )

      // Driver selection should be set to "Specific" and show driver field
      cy.get('[data-testid="StepThree-DriverSelect"]').should('be.visible')

      // Vehicle commander selection should be set to "Specific" and show commander field
      cy.get('[data-testid="StepThree-VehicleCommanderSelect"]').should('be.visible')

      // Should show vehicle registration field
      cy.findByTestId('StepThree-VehicleId').should('be.visible')

      // Should not show vehicle type field
      cy.findByTestId('StepThree-VehicleType').should('not.exist')

      // Approve button should be disabled
      cy.get('[data-testid="ViewApproveBookingDrawer-Approve"]').should('be.disabled')
      // TODO: fill the vehicle registration field

      cy.get('[data-testid="ViewApproveBookingDrawer-Approve"]').click()

      cy.get('@onSubmit').should('have.been.called')
    })

    it('should show loading state on Approve button during submission', () => {
      mountViewApproveBookingDrawer({
        mode: 'approve',
        onApprovalLoading: true,
      })

      cy.wait('@getBookingData')

      // Approve button should show loading state
      cy.contains('Approve').should('be.disabled')
      cy.get('.MuiCircularProgress-root').should('be.visible')
    })
  })

  describe('Rejection Functionality', () => {
    it('should open reject modal when Reject button is clicked', () => {
      const onReject = cy.stub().as('onReject')
      mountViewApproveBookingDrawer({ mode: 'approve', onReject })

      cy.wait('@getBookingData')

      cy.get('[data-testid="ViewApproveBookingDrawer-Reject"]').click()

      // Reject modal should be visible
      cy.get('[data-testid*="reject-modal"]').should('be.visible')

      // Fill reject form and confirm
      cy.get('[data-testid*="reject-reason"]').click()
      cy.contains('Vehicle not available').click()
      cy.get('[data-testid*="reject-remarks"]').type('Not available for this time slot')
      cy.contains('Confirm').click()

      cy.get('@onReject').should('have.been.called')
    })

    it('should close reject modal when cancelled', () => {
      mountViewApproveBookingDrawer({ mode: 'approve' })

      cy.wait('@getBookingData')

      cy.get('[data-testid="ViewApproveBookingDrawer-Reject"]').click()

      // Reject modal should be visible
      cy.get('[data-testid*="reject-modal"]').should('be.visible')

      // Cancel the rejection
      cy.contains('Cancel').last().click()

      // Modal should be closed
      cy.get('[data-testid*="reject-modal"]').should('not.exist')
    })
  })

  describe('Permission-based Rendering', () => {
    it('should hide Approve button when user lacks approval permission', () => {
      mountWithProviders(<TestViewApproveBookingDrawer mode="approve" />, {
        reduxOptions: {
          preloadedState: {
            user: duxsMocks.user({
              carpoolApproveBookings: false,
              carpoolDeclineBookings: true,
            }).mockState,
          },
        },
      })

      cy.wait('@getBookingData')

      cy.contains('Approve').should('not.exist')
      cy.contains('Reject').should('be.visible')
    })

    it('should hide Reject button when user lacks decline permission', () => {
      mountWithProviders(<TestViewApproveBookingDrawer mode="approve" />, {
        reduxOptions: {
          preloadedState: {
            user: duxsMocks.user({
              carpoolApproveBookings: true,
              carpoolDeclineBookings: false,
            }).mockState,
          },
        },
      })

      cy.wait('@getBookingData')

      cy.contains('Approve').should('be.visible')
      cy.contains('Reject').should('not.exist')
    })
  })

  describe('Close Functionality', () => {
    it('should call onClose when Cancel button is clicked', () => {
      const onClose = cy.stub().as('onClose')
      mountViewApproveBookingDrawer({ mode: 'view', onClose })

      cy.wait('@getBookingData')

      cy.get('[data-testid="ViewApproveBookingDrawer-Cancel"]').click()
      cy.get('@onClose').should('have.been.called')
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      // Mock API error
      cy.intercept('POST', '/jsonrpc/public/index.php', {
        statusCode: 500,
        body: { error: 'Internal server error' },
      })

      const onClose = cy.stub().as('onClose')
      mountViewApproveBookingDrawer({ onClose })

      // Should call onClose when there's an error
      cy.get('@onClose').should('have.been.called')
    })
  })

  describe('Form Validation in Approve Mode', () => {
    it('should validate required fields in approve mode', () => {
      mountViewApproveBookingDrawer({ mode: 'approve' })

      cy.wait('@getBookingData')

      // Clear required fields
      cy.findByLabelText(/Vehicle registrations/i).clear()
      cy.findByLabelText(/Select Driver/i).clear()

      // Try to approve
      cy.contains('Approve').should('be.disabled')

      // Should show validation errors
      cy.contains(messages.required).should('be.visible')
    })
  })
})
