/* eslint-disable no-param-reassign */
/// <reference types="@testing-library/cypress" />
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { createMemoryHistory } from 'history'
import { match } from 'ts-pattern'

import type { Ct_fleet_delete_favourite_report } from 'api/reports/types'
import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { allReports, reportEndpointMocks } from 'src/cypress-ct/mocks/endpoints/report'
import { exhaustiveEndpointCallCheck, mountWithProviders } from 'src/cypress-ct/utils'

import AllReports from './AllReports'
import { REPORT_SIDEBAR_WIDTH } from './util'

const basePath = '/reports'
const defaultSelectedReport = allReports[0]
const defaultSelecteId = defaultSelectedReport.report_id

const globalHistory = createMemoryHistory({
  initialEntries: [basePath],
})

const mountAllReportsMain = () => {
  mountWithProviders(<AllReports />, {
    history: globalHistory,
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user({}).mockState,
      },
    },
  })
}

const handleBaseCyEndpoints = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_prelogin_data' }, () => {
      req.reply({ delay: 50, body: { id: 10, result: {} } })
    })
    .with({ method: 'ct_fleet_add_favourite_report' }, () => {
      req.reply({
        delay: 50,
        body: {
          id: 10,
          result: {
            ct_fleet_get_customized_reports: [],
            ct_fleet_get_favourite_reports: [],
          },
        },
      })
    })
    .with({ method: 'ct_fleet_delete_favourite_report' }, () => {
      req.reply({
        delay: 50,
        body: {
          id: 10,
          result: {
            ct_fleet_get_customized_reports: [],
            ct_fleet_get_favourite_reports: [],
          } satisfies Ct_fleet_delete_favourite_report.ApiOutput,
        },
      })
    })
    .with({ method: 'ct_fleet_get_report_preview' }, () => {
      req.reply({
        fixture: 'dummy.pdf',
      })
    })
    .with({ method: 'ct_fleet_get_report_options_v2' }, () => {
      req.alias = 'getAllReports'
      req.reply(reportEndpointMocks.ct_fleet_get_report_options_v2())
    })
    .with({ method: 'ct_fleet_get_favourite_reports' }, () => {
      req.alias = 'getFavoriteReports'
      req.reply(reportEndpointMocks.ct_fleet_get_favourite_reports())
    })
    .with({ method: 'ct_fleet_get_customized_reports' }, () => {
      req.reply(reportEndpointMocks.ct_fleet_get_customized_reports())
    })
    .with({ method: 'ct_fleet_get_custom_report_resources' }, () => {
      req.reply(reportEndpointMocks.ct_fleet_get_custom_report_resources())
    })
    .otherwise(exhaustiveEndpointCallCheck)

beforeEach(() => {
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    handleBaseCyEndpoints(req)
  })
})

describe('All reports', () => {
  it('show the all reports with sidebar', () => {
    mountAllReportsMain()

    // show sidebar
    cy.findByTestId('Reports-AllReports-Sidebar-Title').should(
      'have.text',
      'Browse All Reports',
    )
    // sidebar should have fixed width
    cy.findByTestId('Reports-AllReports-Sidebar').should(
      'have.css',
      'width',
      `${REPORT_SIDEBAR_WIDTH}px`,
    )

    // sidebar tab
    cy.findByTestId('Reports-AllReports-Sidebar-Tabs').should(
      'have.css',
      'width',
      '112px',
    )
    cy.findByTestId('Reports-AllReports-Sidebar-Tabs')
      .find('.MuiTabs-flexContainer')
      .first()
      .children()
      .should('have.length', 6)

    // the first three tabs should be sticky
    cy.findByTestId('Reports-AllReports-Sidebar-AllTab').should(
      'have.css',
      'position',
      'sticky',
    )
    cy.findByTestId('Reports-AllReports-Sidebar-AllTab').should(
      'have.class',
      'Mui-selected',
    )
    cy.findByTestId('Reports-AllReports-Sidebar-FavTab').should(
      'not.have.class',
      'Mui-selected',
    )
    cy.findByTestId('Reports-AllReports-Sidebar-FavTab').should(
      'have.css',
      'position',
      'sticky',
    )
    cy.findByTestId('Reports-AllReports-Sidebar-CusTab').should(
      'have.css',
      'position',
      'sticky',
    )

    // by default the first report in the list is selected
    cy.findByTestId(
      `Reports-AllReports-SidebarItem-${defaultSelecteId}-FavIcon`,
    ).should('exist')
    cy.findByTestId(
      `Reports-AllReports-SidebarItem-${defaultSelecteId}-ExportButton`,
    ).should('not.be.visible')
    cy.findByTestId('Reports-AllReports-SidebarItem-2-FavIcon').should('not.exist')
    cy.findByTestId(`Reports-AllReports-SidebarItem-${defaultSelecteId}`)
      .find('.MuiListItemButton-root')
      .should('have.class', 'Mui-selected')

    // the export form should be visible
    cy.findByTestId('Report-OneTimeExportForm').should('exist')
  })

  it('search text', () => {
    mountAllReportsMain()

    // go to favorite tab first
    cy.findByTestId('Reports-AllReports-Sidebar-FavTab').click()

    // type text in the search field
    cy.findByTestId('Reports-AllReports-Sidebar-Search').type('2')

    // should jump to the tab All
    cy.findByTestId('Reports-AllReports-Sidebar-AllTab').should(
      'have.class',
      'Mui-selected',
    )

    // the list should be filtered
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .should('have.length', 1)

    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .first()
      .find('.MuiListItemText-root')
      .should('have.text', allReports[1].name)

    // the search result should not be selected
    cy.findByTestId(`Reports-AllReports-SidebarItem-2`)
      .find('.MuiListItemButton-root')
      .should('not.have.class', 'Mui-selected')

    // click the favorite tab
    cy.findByTestId('Reports-AllReports-Sidebar-FavTab').click()
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .should('have.length', 0)
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('p')
      .first()
      .should('have.text', 'No results found.')
  })

  it('set report as unfavorite by clicking on the icon on sidebar list item', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_delete_favourite_report' }, () => {
          req.alias = 'deleteFavorite'
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result: {
                ct_fleet_get_customized_reports: [],
                ct_fleet_get_favourite_reports: [],
              },
            },
          })
        })
        .otherwise(() => handleBaseCyEndpoints(req))
    })

    mountAllReportsMain()

    // click the default selected report favorite icon to remove favorite
    cy.findByTestId(
      `Reports-AllReports-SidebarItem-${defaultSelecteId}-FavIcon`,
    ).click()

    cy.wait('@deleteFavorite')
    cy.findByTestId(
      `Reports-AllReports-SidebarItem-${defaultSelecteId}-FavIcon`,
    ).should('not.exist')
  })

  it('set report as unfavorite by clicking on the icon on favorite sidebar list item', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_delete_favourite_report' }, () => {
          req.alias = 'deleteFavorite'
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result: {
                ct_fleet_get_customized_reports: [],
                ct_fleet_get_favourite_reports: [],
              },
            },
          })
        })
        .otherwise(() => handleBaseCyEndpoints(req))
    })

    mountAllReportsMain()

    // favorite tab
    cy.findByTestId('Reports-AllReports-Sidebar-FavTab').click()
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .should('have.length', 1)

    // click the default selected report favorite icon to remove favorite
    cy.findByTestId(
      `Reports-AllReports-SidebarItem-${defaultSelecteId}-FavIcon`,
    ).click()

    cy.wait('@deleteFavorite')

    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .should('have.length', 0)
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('p')
      .first()
      .should('have.text', "You haven't added any favourite reports yet")

    // no content
    cy.findByTestId('Reports-AllReports-Content-NoContent-Button').should(
      'have.text',
      'Go to all reports',
    )
  })

  it('set report as favorite by clicking the favorite icon in the sidebar list item', () => {
    mountAllReportsMain()
    cy.findByTestId('Reports-AllReports-Sidebar-AllTab').click()
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_add_favourite_report' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result: {
                ct_fleet_get_customized_reports: [],
                ct_fleet_get_favourite_reports: [
                  {
                    report_description: 'asdf',
                    report_id: '2',
                    report_name:
                      'Report 2 No download, dateRange with DateTime, vehicle no all, geofence group, username',
                    report_type: 'favourite',
                  },
                ],
              },
            },
          })
        })
        .otherwise(() => handleBaseCyEndpoints(req))
    })

    // click the default selected report favorite icon to remove favorite
    cy.findByTestId('Reports-AllReports-SidebarItem-2-UnfavIcon').invoke('show').click()

    cy.findByTestId('Reports-AllReports-SidebarItem-2-FavIcon').should('exist')
  })

  it('empty for favorite or customized reports', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_fleet_get_favourite_reports' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result: {
                ct_fleet_get_customized_reports: [],
                ct_fleet_get_favourite_reports: [],
              },
            },
          })
        })
        .with({ method: 'ct_fleet_get_customized_reports' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result: {
                ct_fleet_get_customized_reports: [],
              },
            },
          })
        })
        .otherwise(() => handleBaseCyEndpoints(req))
    })

    mountAllReportsMain()

    // fav tab
    cy.findByTestId('Reports-AllReports-Sidebar-FavTab').click()
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .should('have.length', 0)
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('p')
      .first()
      .should('have.text', "You haven't added any favourite reports yet")

    // no content
    cy.findByTestId('Reports-AllReports-Content-NoContent-Button').should(
      'have.text',
      'Go to all reports',
    )

    // custom tab
    cy.findByTestId('Reports-AllReports-Sidebar-CusTab').click()
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('li.MuiListItem-root')
      .should('have.length', 0)
    cy.findByTestId('Reports-AllReports-Sidebar-List')
      .find('p')
      .first()
      .should('have.text', "You haven't added any custom reports yet")
    cy.findByTestId('Reports-AllReports-Sidebar-CreateCustomButton').should('exist')

    // no content
    cy.findByTestId('Reports-AllReports-Content-NoContent-Button').should(
      'have.text',
      'Create Custom Report',
    )
  })

  it('Click the export button to show the export report drawer', () => {
    mountAllReportsMain()

    // click the export button
    cy.findByTestId(`Reports-AllReports-SidebarItem-${defaultSelecteId}-ExportButton`)
      .invoke('show')
      .click()

    cy.findByTestId('Reports-Export-Drawer').should('be.visible')
  })
})
