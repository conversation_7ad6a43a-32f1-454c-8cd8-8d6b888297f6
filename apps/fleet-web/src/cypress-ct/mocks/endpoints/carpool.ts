// Mock data for carpool endpoints - using string literal for BookingStatus to avoid import issues
import { parseDrivers } from 'api/drivers'
import type { Ct_fleet_get_drivers_with_eld } from 'src/api/drivers/types'

type BookingStatus = 'pending' | 'approved' | 'rejected' | 'completed'

export const mockBookingData = {
  id: 1,
  purposeOfRequest: 1,
  requestDescription: 'Test booking description',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: 1,
  vehicleId: 1,
  numberOfPassengers: 3,
  driverSelection: 'specific' as const,
  driverId: 1,
  vehicleCommanderSelection: 'self' as const,
  vehicleCommanderId: null,
  pickupTime: '2024-01-15T09:00:00.000Z',
  pickupLocation: 1,
  dropoffTime: '2024-01-15T17:00:00.000Z',
  journeyType: 'one-way' as const,
  equipmentType: 'car-boot' as const,
  equipmentIds: [1],
  remarks: 'Test remarks',
  status: 'pending' as BookingStatus,
}

const mockRawDriverData: Ct_fleet_get_drivers_with_eld.ApiOutput['ct_fleet_get_drivers_with_eld'] =
  [
    {
      out_eld_status: 'active',
      out_current_timezone_setting: null,
      out_department_ids: ['1'],
      out_driver_id: '1',
      out_driver_name: 'John Doe',
      out_driver_email: '<EMAIL>',
      out_driver_surname: null,
      out_driver_owner: 'admin',
      out_main_office_address: '123 Main St',
      out_driver_description: 'Senior Driver',
      out_gender: 'M',
      out_driver_behaviour_score: 85,
      out_vehicle_registration: null,
      out_vehicle_type_id: '0',
      out_driver_statuses: ['10'],
      out_eld_status_time: null,
      out_total_shift_hours: '8:00',
      driver_login_username: 'john.doe',
      out_driver_login_username: 'john.doe',
      out_cell_phone: '+*********0',
      out_license_number: 'DL123456',
      out_license_code: null,
      out_vehicle_id: null,
      out_time_until_break: null,
      out_shift_time_available: null,
      out_current_status_time_available: null,
      out_time_availabe_tomorrow: null,
      out_on_duty_time: null,
      out_summary_ts: null,
      out_received_ts: null,
      out_driver_duty_status: null,
      out_cycle_rule_description: null,
      out_current_timezone_description: null,
      out_app_mode: null,
      out_allow_yard_move: null,
      out_allow_personal_conveyance: null,
      out_new_defects: null,
      out_repaired_defects: null,
      out_inspected_defects: null,
      out_license_expiration_timestamp: '1735689600000', // 2025-12-31
      out_permissions: {
        edit: true,
        remove: true,
      },
      out_id_passport_number: '*********',
      out_employee_number: 1001,
      out_social_security_number: null,
      out_manager_name: 'Manager One',
      out_hired_date: '2020-01-15',
      out_termination_date: null,
      out_driver_license_types: 'Class C',
      out_driver_special_license_types: '',
      out_cycle_working_time: '40:00',
    },
    {
      out_eld_status: 'active',
      out_current_timezone_setting: null,
      out_department_ids: ['2'],
      out_driver_id: '2',
      out_driver_name: 'Jane Smith',
      out_driver_email: '<EMAIL>',
      out_driver_surname: null,
      out_driver_owner: 'admin',
      out_main_office_address: '456 Oak Ave',
      out_driver_description: 'Experienced Driver',
      out_gender: 'F',
      out_driver_behaviour_score: 92,
      out_vehicle_registration: null,
      out_vehicle_type_id: '0',
      out_driver_statuses: ['10'],
      out_eld_status_time: null,
      out_total_shift_hours: '8:00',
      driver_login_username: 'jane.smith',
      out_driver_login_username: 'jane.smith',
      out_cell_phone: '+1*********',
      out_license_number: 'DL789012',
      out_license_code: null,
      out_vehicle_id: null,
      out_time_until_break: null,
      out_shift_time_available: null,
      out_current_status_time_available: null,
      out_time_availabe_tomorrow: null,
      out_on_duty_time: null,
      out_summary_ts: null,
      out_received_ts: null,
      out_driver_duty_status: null,
      out_cycle_rule_description: null,
      out_current_timezone_description: null,
      out_app_mode: null,
      out_allow_yard_move: null,
      out_allow_personal_conveyance: null,
      out_new_defects: null,
      out_repaired_defects: null,
      out_inspected_defects: null,
      out_license_expiration_timestamp: '1723680000000', // 2025-08-15
      out_permissions: {
        edit: true,
        remove: false,
      },
      out_id_passport_number: '*********',
      out_employee_number: 1002,
      out_social_security_number: null,
      out_manager_name: 'Manager Two',
      out_hired_date: '2019-03-20',
      out_termination_date: null,
      out_driver_license_types: 'Class C',
      out_driver_special_license_types: 'CDL',
      out_cycle_working_time: '42:30',
    },
  ]

export const mockParsedDriverData = parseDrivers(mockRawDriverData, [], []).drivers

export const mockEquipmentData = [
  {
    id: 1,
    accessoryTypeId: 1,
    accessoryTypeName: 'Electronics',
    name: 'Laptop',
    description: 'Standard business laptop',
  },
  {
    id: 2,
    accessoryTypeId: 1,
    accessoryTypeName: 'Electronics',
    name: 'Projector',
    description: 'Portable presentation projector',
  },
  {
    id: 3,
    accessoryTypeId: 2,
    accessoryTypeName: 'Tools',
    name: 'Toolkit',
    description: 'Basic maintenance toolkit',
  },
]

// Endpoint mock functions
export const carpoolEndpointMocks = {
  ct_fleet_get_available_vehicles: () => ({
    body: {
      id: 10,
      result: [
        { id: 1, registration: 'ABC123', vehicleTypeId: 1 },
        { id: 2, registration: 'DEF456', vehicleTypeId: 2 },
      ],
    },
  }),
  ct_fleet_get_booking_options: () => ({
    body: {
      id: 10,
      result: {
        equipments: [
          { id: 1, name: 'Laptop' },
          { id: 2, name: 'Projector' },
        ],
        drivers: [
          { id: 1, name: 'John Doe' },
          { id: 2, name: 'Jane Smith' },
        ],
      },
    },
  }),
  ct_fleet_carpool_get_booking_options: () => ({
    body: {
      id: 10,
      result: {
        booking_purposes: [
          {
            booking_purpose_id: 1,
            booking_purpose: 'Official Business',
            purpose_allowed_vehicle: [
              { vehicle_type_id: 1, vehicle_type: 'Sedan' },
              { vehicle_type_id: 2, vehicle_type: 'SUV' },
              { vehicle_type_id: 3, vehicle_type: 'Van' },
            ],
          },
          {
            booking_purpose_id: 2,
            booking_purpose: 'Personal Use',
            purpose_allowed_vehicle: [
              { vehicle_type_id: 1, vehicle_type: 'Sedan' },
              { vehicle_type_id: 2, vehicle_type: 'SUV' },
            ],
          },
        ],
        booking_vehicle_types: [
          { booking_vehicle_type_id: 1, booking_vehicle_type: 'Sedan' },
          { booking_vehicle_type_id: 2, booking_vehicle_type: 'SUV' },
          { booking_vehicle_type_id: 3, booking_vehicle_type: 'Van' },
        ],
        cancel_reasons: [
          {
            booking_cancel_reason_id: 1,
            booking_cancel_reason: 'Change of plans',
            enabled: true,
          },
          {
            booking_cancel_reason_id: 2,
            booking_cancel_reason: 'Emergency',
            enabled: true,
          },
        ],
        drivers: [
          {
            client_driver_id: '1',
            driver_name: 'John Doe',
            driver_special_license_types: [{ id: '1', expiry: '2025-12-31' }],
            driver_license_types: [
              { id: '1', expiry: '2025-06-30' },
              { id: '2', expiry: null },
            ],
            driver_department_ids: ['1', '2'],
          },
          {
            client_driver_id: '2',
            driver_name: 'Jane Smith',
            driver_special_license_types: [],
            driver_license_types: [{ id: '1', expiry: '2025-08-15' }],
            driver_department_ids: ['1'],
          },
        ],
        locations: [
          {
            site_location_id: 1,
            site_location_type: 'Office',
            site_location_name: 'Main Office',
          },
          {
            site_location_id: 2,
            site_location_type: 'Branch',
            site_location_name: 'Branch Office',
          },
          {
            site_location_id: 3,
            site_location_type: 'Warehouse',
            site_location_name: 'Warehouse',
          },
        ],
        vehicles: [
          {
            carpool_enabled: true,
            vehicle: 'ABC123 - Sedan',
            vehicle_id: '1',
            fleet_vehicle_type: 'Sedan',
            booking_vehicle_type_id: '1',
            required_special_licenses: [],
            driver_license_type_ids: ['1'],
            default_site_location_id: '1',
            departments: [
              { department_id: '1', department_name: 'IT Department' },
              { department_id: '2', department_name: 'HR Department' },
            ],
            common_pool: 't',
          },
          {
            carpool_enabled: true,
            vehicle: 'DEF456 - SUV',
            vehicle_id: '2',
            fleet_vehicle_type: 'SUV',
            booking_vehicle_type_id: '2',
            required_special_licenses: [],
            driver_license_type_ids: ['1', '2'],
            default_site_location_id: '2',
            departments: [{ department_id: '1', department_name: 'IT Department' }],
            common_pool: 'f',
          },
          {
            carpool_enabled: true,
            vehicle: 'GHI789 - Van',
            vehicle_id: '3',
            fleet_vehicle_type: 'Van',
            booking_vehicle_type_id: '3',
            required_special_licenses: ['1'],
            driver_license_type_ids: ['1', '2'],
            default_site_location_id: '3',
            departments: [{ department_id: '2', department_name: 'HR Department' }],
            common_pool: 't',
          },
        ],
      },
    },
  }),
  ct_fleet_get_additional_locations: () => ({
    body: {
      id: 10,
      result: [
        { id: 1, locationName: 'Additional Location 1' },
        { id: 2, locationName: 'Additional Location 2' },
      ],
    },
  }),
}

const wrapRestResponse = <T>(response: T) => ({
  status: 200,
  body: {
    Success: true,
    value: response,
  },
})

// REST API mocks for equipment and vehicle-for-driver endpoints
export const restApiMocks = {
  // Mock for /scdf/accessory endpoint (useEquipmentsQuery)
  'GET /scdf/accessory': () =>
    wrapRestResponse({
      data: mockEquipmentData,
      totalCount: mockEquipmentData.length,
      message: null,
    }),

  // Mock for /scdf/vehicle/vehicle-for-driver endpoint (useVehiclesForDriverQuery)
  'GET /scdf/vehicle/vehicle-for-driver': (params?: { clientDriverId?: string }) => {
    // Return different vehicles based on driver ID
    const driverId = params?.clientDriverId
    let result: Array<any> = []

    if (driverId === '1') {
      result = [
        {
          vehicleId: 1,
          registration: 'ABC123',
          vehicleName: 'Sedan 1',
          clientVehicleDescription: 'Company sedan',
          fuelTargetConsumption: '8.5',
          vehicleEngineTypeId: '1',
          maintenanceId: null,
          defaultSiteLocationId: 1,
          isPoolActive: true,
          maintenanceStatusId: null,
          bookingVehicleTypeId: 1,
          vehicleStatusOptionId: 1,
          bookingAllocationPriority: 1,
          speedSourceId: 1,
          defaultDriver: 'John Doe',
          createdTs: '2024-01-01T00:00:00Z',
          updatedTs: '2024-01-01T00:00:00Z',
          departments: [
            { id: 1, name: 'IT Department' },
            { id: 2, name: 'HR Department' },
          ],
          driverLicenses: [{ licenseTypeId: 1, licenseName: 'Standard License' }],
          specialDriverLicenses: [],
        },
        {
          vehicleId: 2,
          registration: 'DEF456',
          vehicleName: 'SUV 1',
          clientVehicleDescription: 'Company SUV',
          fuelTargetConsumption: '12.0',
          vehicleEngineTypeId: '2',
          maintenanceId: null,
          defaultSiteLocationId: 2,
          isPoolActive: false,
          maintenanceStatusId: null,
          bookingVehicleTypeId: 2,
          vehicleStatusOptionId: 1,
          bookingAllocationPriority: 2,
          speedSourceId: 1,
          defaultDriver: 'John Doe',
          createdTs: '2024-01-01T00:00:00Z',
          updatedTs: '2024-01-01T00:00:00Z',
          departments: [{ id: 1, name: 'IT Department' }],
          driverLicenses: [
            { licenseTypeId: 1, licenseName: 'Standard License' },
            { licenseTypeId: 2, licenseName: 'Commercial License' },
          ],
          specialDriverLicenses: [],
        },
      ]
    } else if (driverId === '2') {
      result = [
        {
          vehicleId: 3,
          registration: 'GHI789',
          vehicleName: 'Van 1',
          clientVehicleDescription: 'Company van',
          fuelTargetConsumption: '15.0',
          vehicleEngineTypeId: '3',
          maintenanceId: null,
          defaultSiteLocationId: 3,
          isPoolActive: true,
          maintenanceStatusId: null,
          bookingVehicleTypeId: 3,
          vehicleStatusOptionId: 1,
          bookingAllocationPriority: 3,
          speedSourceId: 1,
          defaultDriver: 'Jane Smith',
          createdTs: '2024-01-01T00:00:00Z',
          updatedTs: '2024-01-01T00:00:00Z',
          departments: [{ id: 2, name: 'HR Department' }],
          driverLicenses: [{ licenseTypeId: 1, licenseName: 'Standard License' }],
          specialDriverLicenses: [
            { licenseTypeId: 1, licenseName: 'Heavy Vehicle License' },
          ],
        },
      ]
    }

    // Return empty array for unknown driver or no driver
    return wrapRestResponse(result)
  },

  'POST /scdf/booking/uploadSingleImageTemp': () =>
    wrapRestResponse({
      guid: 'mock-guid-' + Date.now(),
      contentType: 'image/jpeg',
      extension: 'jpg',
      status: 'success',
    }),
  'POST /scdf/booking/deleteTempSingleImage': () =>
    wrapRestResponse({
      guid: 'mock-guid',
      extension: 'jpg',
      status: 'success',
    }),
}
