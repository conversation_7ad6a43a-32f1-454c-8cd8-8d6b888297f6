import { defaultPreferences } from 'duxs/user'
import { initialState as fleetMapViewInitialState } from 'src/modules/map-view/FleetMapView/DetailsPanel/slice'
import {
  initialState as driversInitialState,
  type State as DriverState,
} from 'duxs/drivers'
import { initialState as reportsInitialState } from 'duxs/reports'
import type { MapApiProvider } from 'api/user/types'
import type user from 'duxs/user'
import type geofences from 'duxs/geofences'
import type vehiclesReducer from 'duxs/vehicles'
import type { ReduxVehicleGroups, ReduxVehicles } from 'duxs/vehicles'
import type { reducer } from 'duxs/admin'
import type { ClientUserId, DriverId } from 'api/types'
import type { dashboardView } from 'src/modules/dashboard/combined-slice'
import type { ISO3166_1Alpha2CountryCode } from 'src/types'

export const duxsMocks = {
  user: ({
    customFormActionCreate = true,
    customFormActionDelete = true,
    customFormActionEdit = true,
    vehicleInspectionActionApprove = true,
    ...options
  }: {
    defaultTimezone?: string
    mapApiProvider?: MapApiProvider
    availableMapApiProviders?: Array<MapApiProvider>
    geofencesImportGeofences?: boolean
    geofencesAddGroup?: boolean
    geofencesAddGeofence?: boolean
    geofencesDeleteGeofence?: boolean
    listMaintenance?: boolean
    isSubUser?: boolean
    customFormActionCreate?: boolean
    customFormActionEdit?: boolean
    customFormActionDelete?: boolean
    vehicleInspectionActionApprove?: boolean
  } = {}) => ({
    mockState: {
      hubConnectionMeta: null,
      jwtAccessToken: 'eyJhbGciasdasdafasdfsa',
      refreshJwtMutation: { status: 'success' },
      refreshTokenTimeoutId: null,
      vehicleIdToSelectOnMapAfterLogin: undefined,
      user: {
        account: 'CART00003',
        username: 'CART00003',
        id: '227020',
        companyName: '',
        cuid: null,
        primaryEmail: '',
      },
      timeZones: null,
      diagnosticStatus: {},
      diagnostic: {},
      diagnosticFaulty: {},
      diagnosticHealthy: {},
      preferences: defaultPreferences,
      rememberedUsername: '',
      debug: false,
      allowEditSensor: false,
      disabled: undefined,
      disable_reason: '',
      passwordAge: '',
      passwordUpdateStatus: {
        updatedTS: new Date(),
        isSuccess: true,
        response: '',
      },
      preLoginQuery: {
        fetchStatus: 'idle',
        status: 'pending',
      },
      locale: undefined,
      hardwareTypeList: undefined,
      loginApiData: { status: 'IDLE' },
      logoutMutation: { status: 'idle' },
      systemStateMessage: undefined,
      languageList: undefined,
      ctCountriesList: [],
      federatedLogins: undefined,
      settings: {
        defaultissuingcountry: 'SG' satisfies ISO3166_1Alpha2CountryCode,
        apiSettingsTab: true,
        styleProperties: {
          styleAppName: 'Cartrack',
          styleCustomerMainLogo: '',
          styleCustomerMenuLogo: '',
          styleFavicon: 'favicon.png',
          styleNavbarColour: '#333',
          styleSubnavbarColour: '#444',
          styleSidebarMenuHeaderIconPath: '',
          styleSidebarMenuFooterLogoPath: '/assets/cartrack-main-logo-white.svg',
          styleSidebarMenuFooterIconPath: '/assets/cartrack-menu-icon-black.png',
          styleSidebarMenuActiveFontColour: '#FFFFFF',
          styleSidebarMenuInactiveFontColour: '#E0E0E0',
          styleSidebarMenuHoverFontColour: '#FFF',
          styleSidebarMenuHoverColour: '#424242',
          styleSidebarMenuActiveColour: '#616161',
          styleSidebarSubMenuInactiveFontColour: '#BDBDBD',

          styleMenuActiveFontColour: '#FFFFFF',
          styleMenuInactiveFontColour: '#E0E0E0',
          styleActiveButtonsColour: '#f47735',
          styleMenuActiveIconColour: '#FFFFFF',
          styleMenuInactiveIconColour: '#CCCCCC',
          styleSubmenuActiveColour: '#F47735',
          styleTableColumnFontColourActive: '#F47735',
          styleInputfieldColourActive: '#F47735',
          styleInputfieldColourSelection: '#F47735',
          styleInputfieldColourHover: '#EEEEEE',
          styleInputfieldIconColourStandard: '#666666',
          styleInputfieldIconColourActive: '#F47735',
          styleLoadingBarColour: '#F47735',
          styleLoadingSpinnerColour: '#F47735',
          styleIconColour: '#F47735',
          styleAlternateTextColour: '#F47735',
          styleSuperscriptCounterColour: '#F47735',
          styleButtonDefaultColourActive: '#F47735',
          styleButtonDefaultTextColourActive: '#FFFFFF',
          styleButtonDefaultColourStandard: '#FFFFFF',
          styleButtonDefaultTextColourStandard: '#666666',
          styleButtonDefaultColourHover: '#FFFFFF',
          styleButtonDefaultTextColourHover: '#F47735',
          styleButtonDefaultColourDisabled: '#F9F9F9',
          styleButtonDefaultTextColourDisabled: '#666666',
          styleButtonActionColourActive: '#23567C',
          styleButtonActionTextColourActive: '#FFFFFF',
          styleButtonActionColourStandard: '#5390BC',
          styleButtonActionTextColourStandard: '#FFFFFF',
          styleButtonActionColourHover: '#2E72A4',
          styleButtonActionTextColourHover: '#FFFFFF',
          styleButtonActionColourDisabled: '#5390BC',
          styleButtonActionTextColourDisabled: '#F9F9F9',
          styleButtonLinkColourActive: '',
          styleButtonLinkTextColourActive: '#23567C',
          styleButtonLinkColourStandard: '',
          styleButtonLinkTextColourStandard: '#5390BC',
          styleButtonLinkColourHover: '',
          styleButtonLinkTextColourHover: '#2E72A4',
          styleButtonLinkColourDisabled: '',
          styleButtonLinkTextColourDisabled: '#5390BC',
          styleButtonRedColourActive: '#963825',
          styleButtonRedTextColourActive: '#FFFFFF',
          styleButtonRedColourStandard: '#FFFFFF',
          styleButtonRedTextColourStandard: '#CE5239',
          styleButtonRedColourHover: '#CE5239',
          styleButtonRedTextColourHover: '#FFFFFF',
          styleButtonRedColourDisabled: '#FFFFFF',
          styleButtonRedTextColourDisabled: '#CB5138',
          styleButtonGreenColourActive: '#3F7F42',
          styleButtonGreenTextColourActive: '#FFFFFF',
          styleButtonGreenColourStandard: '#FFFFFF',
          styleButtonGreenTextColourStandard: '#5CAE60',
          styleButtonGreenColourHover: '#5CAE60',
          styleButtonGreenTextColourHover: '#FFFFFF',
          styleButtonGreenColourDisabled: '#FFFFFF',
          styleButtonGreenTextColourDisabled: '#5CAE60',
          styleButtonRaisedColourActive: '#F47735',
          styleButtonRaisedTextColourActive: '#FFFFFF',
          styleButtonRaisedColourStandard: '#FFFFFF',
          styleButtonRaisedTextColourStandard: '#666666',
          styleButtonRaisedColourHover: '#FFFFFF',
          styleButtonRaisedTextColourHover: '#F47735',
          styleButtonRaisedColourDisabled: '#FFFFFF',
          styleButtonRaisedTextColourDisabled: '#666666',
          styleButtonWhiteColourActive: '#FFFFFF',
          styleButtonWhiteTextColourActive: '#727272',
          styleButtonWhiteColourStandard: '#333333',
          styleButtonWhiteTextColourStandard: '#FFFFFF',
          styleButtonWhiteColourHover: '#8F8F8F',
          styleButtonWhiteTextColourHover: '#FFFFFF',
          styleButtonWhiteColourDisabled: '#333333',
          styleButtonWhiteTextColourDisabled: '#8F8F8F',
          styleMainLogoPoweredBy: false,
          styleMenuLogoPoweredBy: false,
          styleMainLogoWidthPx: '',
          styleLogoPoweredByType: 1,
          styleLoginBackgroundColour: '#F9F9F9',
          styleLoginTextColour: '#3e5762',
          styleLoginFooterLogo: '',
          styleLoginFooterBackgroundColor: '',
          styleLoginFooterHeight: '',
          styleLoginForgotUsername: '',
          styleLoginLanguage: '',
          styleLoginModalFooter: '',
          styleLoginSignUp: '',
          styleLoginStayLoggedIn: '',
          styleLoginLogoInsideBox: '',
          karooUiTheme: null,
        },
        defaultCountry: 'SG',
        visionWatchVideoStream: true,
        vision: true,
        dataUsageBuyMore: true,
        userProfileSettingsEditUser: true,
        adminReminders: true,
        costs: false,
        defaultTimezone:
          options?.defaultTimezone ??
          new Intl.DateTimeFormat().resolvedOptions().timeZone,
        mapApiProvider: options?.mapApiProvider,
        availableMapApiProviders: options?.availableMapApiProviders,
        defaultMapLat: 0,
        defaultMapLon: 0,
        defaultMapZoom: 10,
        currencySymbol: '$',
        sisenseApi: 'http://localhost:50000',
        geofencesImportGeofences: options?.geofencesImportGeofences ?? true,
        geofencesAddGroup: options?.geofencesImportGeofences ?? true,
        geofencesAddGeofence: options?.geofencesImportGeofences ?? true,
        geofencesDeleteGeofence: options?.geofencesImportGeofences ?? true,
        listMaintenance: options?.listMaintenance ?? true,
        primaryEmail: '<EMAIL>',
        isSubUser: options?.isSubUser ?? false,
        dashboardUtilites:
          "{'driver':'false','vehicleName':'false','search':'false','vehicleGroups':'true','registration':'true','vehicleDescription':'true','vehicleDescription2':'true','vehicleDescription3':'true','vehicleType':'false','dateSelection':'true','help':'false','vehicleMake':'false','vehicleModel':'false','downloadAllWidgets':'true','downloadChartWidgets':'true','changeIndustry':'false','addWidgets':'true','deleteWidgets':'true','vehicleDisplayNameToggle':'false'}",
        dashboardLiveUtilities:
          "{'vehicleGroups':'true','registration':'true','vehicleDescription':'false','vehicleDescription2':'false','vehicleDescription3':'false','vehicleName':'false','liveHelp':'true','downloadAllWidgets':'true','downloadChartWidgets':'true','addWidgets':'true','deleteWidgets':'true'}",
        dashboardCustomUtilities:
          "{'driver':'false','vehicleName':'false','search':'false','vehicleGroups':'false','registration':'false','vehicleDescription':'false','vehicleDescription2':'false','vehicleDescription3':'false','vehicleType':'false','dateSelection':'true','vehicleMake':'false','vehicleModel':'false','downloadAllWidgets':'true','downloadChartWidgets':'true','addWidgets':'false','deleteWidgets':'false'}",
        dashboardCoachingUtilities:
          "{'driver':'false','vehicleName':'false','search':'false','vehicleGroups':'false','registration':'false','vehicleDescription':'false','vehicleDescription2':'false','vehicleDescription3':'false','vehicleType':'false','dateSelection':'true','vehicleMake':'false','vehicleModel':'false','downloadAllWidgets':'true','downloadChartWidgets':'true','addWidgets':'false','deleteWidgets':'false'}\n\n",
        customFormActionCreate,
        customFormActionDelete,
        customFormActionEdit,
        vehicleInspectionActionApprove,
      },
      countriesWebsites: [
        {
          countryName: 'sg',
          websiteLink: 'test',
          countryFlagUrl: null,
        },
      ],
      idbStates: {},
    } satisfies ReturnType<typeof user>,
  }),
  admin: {
    loading: false,
    users: [
      {
        username: 'alSUBsg123',
        id: '227020' as ClientUserId,
        status: 'active',
        email: null,
        cellPhone: null,
        departmentIds: null,
        carpoolApproveBookings: true,
        carpoolDeclineBookings: true,
        carpoolEditBookings: true,
        driverId: null,
      },
      {
        username: 'Test Contact',
        id: '123456' as ClientUserId,
        status: 'active',
        email: '<EMAIL>',
        cellPhone: '+351999999999',
        departmentIds: null,
        carpoolApproveBookings: true,
        carpoolDeclineBookings: true,
        carpoolEditBookings: true,
        driverId: 'asdfasd' as DriverId,
      },
    ],
  } satisfies ReturnType<typeof reducer>,
  geofences: {
    geofences: [],
    systemGeofences: undefined,
    groups: [],
    loading: true,
    saveDisabled: false,
  } satisfies ReturnType<typeof geofences>,
  vehicles: ({
    vehicles = [],
    vehicleGroups = [],
  }: {
    vehicles?: ReduxVehicles
    vehicleGroups?: ReduxVehicleGroups
  }): ReturnType<typeof vehiclesReducer> => ({
    wasFirstVehiclesListRequestFetched: true,
    fetchImmobiliseVehicleDataApiStatus: 'idle',
    immobiliseOrActivateVehicleApiStatus: 'idle',
    selectedVehicleId: null,
    vehicles,
    vehicleTypes: [],
    hardwareTypes: [],
    hasLoaded: true,
    vehiclesLoading: false,
    hasLoadedDetails: false,
    immobiliseVehicleMetaData: undefined,
    sharedVehicle: undefined,
    dbits: [],
    cachedVehicles: undefined,
    groups: vehicleGroups,
    trips: [],
    tripsSummaryPanelUI: [], // Used to display the vehicle timelines summary in the details panel
    sensors: [],
    sensorResources: {},
    dbitResources: {},
    vehicleTokens: {},
    isUpdatingVehicleLink: false,
    isFetchingSharedVehicle: false,
    isLoadingSummaryTrips: false,
    dashboardVehicles: [],
    dashboardVehiclesLoaded: false,
    sensorsByName: [],
    otpVehicleId: undefined,
    // Raw Data
    vehicleRawData: [],
    isFetchingRawData: false,

    odometer: [],
    isCreatingVehicleGroup: false,
  }),
  drivers: ({
    drivers = [],
    driverGroups = [],
  }: {
    drivers?: any
    driverGroups?: any
  }) =>
    ({
      ...driversInitialState,
      driversQuery: {
        fetchStatus: 'idle',
        status: 'success',
      },
      drivers,
      groups: driverGroups,
    }) satisfies DriverState,
  fleetMapViewDetailsPanel: {
    detailsPanel: fleetMapViewInitialState,
  },
  dashboardView: {
    setupDashboard: { isNewUser: false, isDashboardInitiation: true },
    sisenseDashboard: {
      liveWidgetLimit: 10,
      liveWidgetCount: 0,
      liveDashboardGeofenceList: [],
      driverList: [],
    },
  } satisfies ReturnType<typeof dashboardView>,
  reports: reportsInitialState,
}
